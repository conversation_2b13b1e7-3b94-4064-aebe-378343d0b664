<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/relativeChip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp">

    <RelativeLayout
        android:id="@+id/rltmainview"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp30">

        <TextView
            android:id="@+id/txt_cat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginEnd="@dimen/dp40"
            android:layout_marginBottom="-30dp"
            android:ellipsize="end"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_0dp"
            android:textStyle="bold"
            android:background="@drawable/join_red_updated_smallsize"/>

        <RelativeLayout
            android:id="@+id/rltAddedChpisview"
            android:layout_width="@dimen/dt_putchips_size"
            android:layout_height="@dimen/dt_putchips_size"
            android:layout_centerInParent="true"
            android:layout_marginRight="@dimen/dp10"
            android:background="@drawable/ic_dt_chips"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvDragonCoins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:ellipsize="end"
                android:padding="3dp"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:text=""
                android:textColor="@color/black"
                android:textSize="7sp"
                android:textStyle="bold"
                android:visibility="visible" />
        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>