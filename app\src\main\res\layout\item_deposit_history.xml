<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:layout_marginTop="0dp"
    android:layout_marginBottom="0dp"
    android:background="@drawable/transpernt_purple"
    android:clickable="true"
    android:foreground="?selectableItemBackground"
    android:focusable="true"
    android:minHeight="0dp"
    android:gravity="center"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="1dp"
        android:paddingBottom="1dp"
        android:paddingLeft="2dp"
        android:paddingRight="2dp"
        android:gravity="center_vertical">

        <!-- Serial Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:gravity="center">

            <TextView
                android:id="@+id/tvSerial"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="1"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Action/View Button Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.7"
            android:gravity="center">

            <Button
                android:id="@+id/btnViewTransaction"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:text="View"
                android:textColor="@color/white"
                android:textSize="7sp"
                android:textStyle="bold"
                android:background="@drawable/transpernt_purple"
                android:layout_margin="0dp"
                android:padding="0dp"
                android:gravity="center"
                android:textAllCaps="false"
                android:minHeight="0dp"
                android:insetTop="0dp"
                android:insetBottom="0dp" />
        </LinearLayout>

        <!-- Phone Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:gravity="center">

            <TextView
                android:id="@+id/tvGames"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="8080808080"
                android:textColor="@color/white"
                android:textSize="9sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Money Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.7"
            android:gravity="center">

            <TextView
                android:id="@+id/txtammount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="₹90"
                android:textColor="@color/green"
                android:textSize="10sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Type Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:gravity="center">

            <TextView
                android:id="@+id/tvType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="-"
                android:textColor="@color/white"
                android:textSize="9sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Status Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.7"
            android:gravity="center">

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="Completed"
                android:textColor="@color/green"
                android:textSize="8sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Time Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.7"
            android:gravity="center">

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:text="03/06\n13:55"
                android:textColor="@color/white"
                android:textSize="7sp"
                android:textStyle="bold"
                android:maxLines="2" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
