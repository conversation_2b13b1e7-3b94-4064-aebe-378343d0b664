<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/background_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/buildbg"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:theme="@style/Theme.MaterialComponents.DayNight.DarkActionBar"
    tools:context=".InitiatePayment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:id="@+id/layout_profile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="25dp"
                android:gravity="center"
                android:text="USDT Payment System"
                android:textColor="#333"
                android:textSize="20sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/login_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:gravity="center"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cd_login"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="7dp"
                    app:cardElevation="0dp"
                    app:cardUseCompatPadding="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- USDT Payment System Header -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="USDT Deposit &amp; Withdrawal"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:layout_marginBottom="20dp"
                            android:textColor="#2E7D32"/>

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Amount (USDT)"
                            android:layout_marginBottom="10dp"
                            app:passwordToggleEnabled="false">
                            <!--this is the actual edit text which takes the input-->
                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_amt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="numberDecimal"
                                android:maxLength="10"
                                android:text="10.00"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:hint="User ID"
                            android:layout_marginBottom="10dp">
                            <!--this is the actual edit text which takes the input-->
                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_user_id"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:text="user123"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Withdrawal Address Field (shown only when needed) -->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:hint="Withdrawal Address (0x...)"
                            android:layout_marginBottom="10dp"
                            android:visibility="visible">
                            <!--this is the actual edit text which takes the input-->
                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_withdraw_address"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text"
                                android:maxLines="2"
                                android:hint="Enter your USDT wallet address for withdrawal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">
                            <!--                        <com.google.android.material.textfield.TextInputLayout-->
                            <!--                            android:layout_width="match_parent"-->
                            <!--                            android:layout_height="wrap_content"-->
                            <!--                            app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout">-->
                            <!--                            <EditText-->
                            <!--                                android:id="@+id/edt_password"-->
                            <!--                                android:layout_width="match_parent"-->
                            <!--                                android:layout_height="45dp"-->
                            <!--                                android:inputType="textPassword"-->
                            <!--                                android:textSize="15sp"-->
                            <!--                                android:hint=""-->
                            <!--                                android:textColor="#333" />-->
                            <!--                        </com.google.android.material.textfield.TextInputLayout>-->
                            <com.google.android.material.textfield.TextInputLayout
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Name"
                                android:visibility="gone"
                                app:passwordToggleEnabled="false">
                                <!--this is the actual edit text which takes the input-->
                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/et_name"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="textEmailAddress"
                                    android:textColor="@color/black" />
                            </com.google.android.material.textfield.TextInputLayout>
                            <!--                        <ImageView-->
                            <!--                            android:id="@+id/show_pass_btn"-->
                            <!--                            android:layout_width="40dp"-->
                            <!--                            android:layout_height="40dp"-->
                            <!--                            android:layout_alignParentRight="true"-->
                            <!--                            android:layout_centerVertical="true"-->
                            <!--                            android:layout_marginRight="5dp"-->
                            <!--                            android:alpha=".5"-->
                            <!--                            android:paddingRight="16dp" />-->
                        </RelativeLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:hint="Note"
                            android:visibility="gone">
                            <!--this is the actual edit text which takes the input-->
                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_note"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Quick Pay Button (Static QR) -->
                        <Button
                            android:id="@+id/btn_quick_pay"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:layout_marginTop="20dp"
                            android:gravity="center"
                            android:backgroundTint="#FF9800"
                            android:textSize="18sp"
                            android:textColor="@color/white"
                            android:textAllCaps="true"
                            android:text="🚀 CLICK TO PAY (SHOW QR)" />

                        <!-- Deposit and Withdrawal Buttons -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginTop="15dp"
                            android:gravity="center">

                            <Button
                                android:id="@+id/btn_deposit"
                                android:layout_width="0dp"
                                android:layout_height="56dp"
                                android:layout_weight="1"
                                android:layout_marginEnd="8dp"
                                android:gravity="center"
                                android:backgroundTint="#2E7D32"
                                android:textSize="16sp"
                                android:textColor="@color/white"
                                android:textAllCaps="true"
                                android:text="DEPOSIT USDT" />

                            <Button
                                android:id="@+id/btn_withdraw"
                                android:layout_width="0dp"
                                android:layout_height="56dp"
                                android:layout_weight="1"
                                android:layout_marginStart="8dp"
                                android:gravity="center"
                                android:backgroundTint="#D32F2F"
                                android:textSize="16sp"
                                android:textColor="@color/white"
                                android:textAllCaps="true"
                                android:text="WITHDRAW USDT" />

                        </LinearLayout>

                        <!-- Information Section -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:text="💡 Information"
                            android:textAlignment="center"
                            android:textColor="#2E7D32"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="• Deposit: Generate QR code and wallet address to receive USDT\n• Withdrawal: Send USDT to your specified wallet address\n• Network: BSC (BEP20) - Low fees and fast transactions"
                            android:textAlignment="center"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>