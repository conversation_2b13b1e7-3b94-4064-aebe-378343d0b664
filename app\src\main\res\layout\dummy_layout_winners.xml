<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/payu_dimen_200dp"
    android:layout_height="@dimen/dp25"
    android:layout_marginBottom="3dp"
    app:cardUseCompatPadding="true"
    app:cardElevation="1dp"
    app:cardCornerRadius="10dp"
    android:background="@drawable/all_players_pannel"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_centerVertical="true"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/lnr_parent"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:padding="@dimen/dp3"
            android:orientation="horizontal">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/img_profile"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:src="@drawable/app_logo_second"
                android:layout_centerVertical="true"
                android:visibility="visible"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="@dimen/dp10"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:elevation="2dp">

                <LinearLayout
                    android:id="@+id/lnrDate"
                    android:layout_width="@dimen/dp100"
                    android:layout_height="@dimen/dp20"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/txt_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:gravity="start|center"
                        android:text="Name"
                        android:singleLine="true"
                        android:ellipsize="marquee"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:scrollHorizontally="true"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrCash"
                    android:layout_width="@dimen/dp50"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1">

                    <TextView
                        android:id="@+id/txt_wallet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="start|center"
                        android:text="0"
                        android:textColor="@color/green" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>