//plugins {
//    id 'com.android.application'
//}
/**/apply plugin: 'com.android.library'

android {
    compileSdk 32

    defaultConfig {
        minSdk 21
        targetSdk 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    //volley
    implementation 'com.android.volley:volley:1.2.1'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'com.squareup.picasso:picasso:2.71828'

//    implementation 'com.shreyaspatil:EasyUpiPayment:2.0'
//    implementation 'dev.shreyaspatil.EasyUpiPayment:EasyUpiPayment:3.0.3'


}