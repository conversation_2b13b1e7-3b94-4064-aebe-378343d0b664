<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    style="@style/dialogParentStyle">


<!--    <ImageView-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        style="@style/popUpBoxbg"-->
<!--        android:layout_alignLeft="@id/lnr_box"-->
<!--        android:layout_alignRight="@id/lnr_box"-->
<!--        android:layout_alignTop="@id/lnr_box"-->
<!--        android:layout_alignBottom="@id/lnr_box"-->
<!--        />-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:background="@drawable/d_pop_setting"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginBottom="@dimen/dp20"
            >

            <LinearLayout
                android:id="@+id/lnrRuleslist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp20"
                android:paddingBottom="@dimen/dp20"
                >

                <TextView
                    android:id="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="@string/how_to_play"
                    android:textAlignment="center"
                    android:textSize="26sp"
                    android:background="@color/cb_dark_blue_button"
                    android:layout_width="match_parent"
                    android:paddingTop="@dimen/dp5"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="  - 30 seconds 1 game, 20 seconds to bet, 10 seconds to show the lottery result. It opens all day.\n

- If you spend 100 to bet, after deducting 2% service fee, your contract amount is 98:\n\n

1. JOIN GREEN: if the result shows 1,3,7,9, you will get (98*2) 196, If the result shows 5, you will get (98*1.5) 147\n\n

2. JOIN RED: if the result shows 2,4,6,8, you will get (98*2) 196,  If the result shows 0, you will get (98*1.5) 147\n\n

3. JOIN VIOLET: if the result shows 0 or 5, you will get (98*4.5) 441,\n\n

4. SELECT NUMBER: if the result is the same as the number you selected, you will get (98*9) 882"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp20"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="300dp"
                    android:visibility="invisible"
                    android:background="@drawable/ic_dt_rule1"
                    />

            </LinearLayout>


        </ScrollView>

    </LinearLayout>


    <include
        layout="@layout/dialog_toolbar"/>



</RelativeLayout>