<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    tools:context=".Activity.Splashscreen">

    <RelativeLayout
        android:id="@+id/rlt_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <ImageView
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:padding="30dp"
            android:src="@drawable/app_logo_second"
            android:visibility="visible" />


        <ImageView
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="50dp"
            android:visibility="gone" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginEnd="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp30"
            android:background="@drawable/progress_bgnew"
            android:indeterminate="true"
            app:trackCornerRadius="@dimen/dp20"
            app:trackThickness="@dimen/dp20" />
    </RelativeLayout>
</RelativeLayout>