<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="40dp"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="40dp"
        android:paddingTop="40dp"
        android:paddingBottom="20dp"
        android:gravity="center"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_marginTop="20dp"
        >

        <Button
            android:id="@+id/create_room_button_free"
            android:layout_width="180dp"
            android:layout_height="70dp"
            android:layout_marginTop="20dp"
            android:src="@drawable/start_button"
            android:adjustViewBounds="true"
            android:scaleType="centerInside"
            android:background="@drawable/button_background_green"
            android:text="Create\nFree Room"
            android:textSize="18dp"
            android:fontFamily="@font/mama_bear"
            android:textColor="@color/white"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:text="or"
            android:fontFamily="@font/mama_bear"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="20dp"
            />
        <TextView
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:text="Number of Diamons per PLayer"
            android:textColor="@color/white"
            android:fontFamily="@font/mama_bear"
            android:layout_marginTop="20dp"
            />
        <EditText
            android:id="@+id/number_of_diamonds_field"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:text="10"
            android:fontFamily="@font/mama_bear"
            android:textSize="20dp"
            android:gravity="center"
            android:background="@drawable/dice"
            android:paddingVertical="10dp"
            android:inputType="number"
            />
        <TextView
            android:id="@+id/errormessageview"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:textColor="@color/red3"
            android:text="Not enough Diamonds"
            android:visibility="invisible"
            />
        <Button
            android:id="@+id/create_room_button_paid"
            android:layout_width="180dp"
            android:layout_height="70dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/start_button"
            android:adjustViewBounds="true"
            android:scaleType="centerInside"
            android:background="@drawable/button_backgrond"
            android:text="Create\nDiamond Room"
            android:fontFamily="@font/mama_bear"
            android:textSize="18dp"
            android:textColor="@color/white"
            android:layout_marginBottom="10dp"
            />
    </LinearLayout>
    <TextView
        android:layout_width="190dp"
        android:layout_height="40dp"
        android:background="@drawable/play_local_written"
        android:layout_centerHorizontal="true"
        android:backgroundTint="@color/yellow3"
        android:text="Create Room"
        android:gravity="center"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold"
        android:fontFamily="@font/oswald"
        />
</RelativeLayout>