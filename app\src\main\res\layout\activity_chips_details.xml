<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    tools:context=".Activity.Splashscreen">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_extra"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/triangle"
            android:layout_width="@dimen/dp30"
            android:layout_height="@dimen/dp30"
            android:layout_centerHorizontal="true"
            android:layout_above="@id/wheel"
            android:tint="@color/blue"
            android:layout_alignParentTop="true"
            android:layout_marginBottom="-30dp"
            app:srcCompat="@drawable/triangle"
            />

        <ImageView
            android:id="@+id/wheel"
            android:layout_width="@dimen/payu_dimen_200dp"
            android:layout_height="@dimen/payu_dimen_200dp"
            android:layout_alignParentTop="true"
            android:layout_centerInParent="true"
            android:layout_marginTop="@dimen/dp25"
            android:adjustViewBounds="true"
            android:rotation="-13"
            android:scaleType="centerInside"
            app:srcCompat="@drawable/wheel_discount" />

        <TextView
            android:id="@+id/resultTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/spinBtn"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:text="you will get 2% discount"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:textColor="@color/yellow1"
            android:textSize="@dimen/payu_dimen_20sp"
            android:textStyle="bold"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/spin_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/wheel"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:text="You can spin only after every 10 minutes"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_14dp"
            android:textStyle="bold"
            android:visibility="visible" />

        <Button
            android:id="@+id/spinBtn"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp40"
            android:layout_below="@id/spin_txt"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginBottom="15dp"
            android:background="@drawable/bg_gredient_white_border"
            android:text="SPIN"
            android:textColor="@color/white" />
    </RelativeLayout>

        <ImageView
            android:id="@+id/imgback"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/back"
            android:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtChipsdetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="Online Payment"
                android:layout_marginTop="@dimen/dp20"
                android:textColor="@color/coloryellow"
                android:textSize="24dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="Fast and secure payment gateway"
                android:layout_marginTop="5dp"
                android:textColor="@color/colordullwhite"
                android:textSize="16dp"
                android:visibility="visible" />

            <!-- Address Field with Copy Icon -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/bg_gredient_white_border"
                android:padding="10dp">

                <TextView
                    android:id="@+id/txt_address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Generating wallet..."
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:maxLines="2"
                    android:ellipsize="middle"
                    android:padding="5dp" />

                <ImageView
                    android:id="@+id/img_copy_address"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/txtcopy"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:padding="4dp" />

            </LinearLayout>

            <!-- Check Status Button and Status Display -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <Button
                    android:id="@+id/btn_check_status"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:text="Check Status"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:background="@drawable/bg_gredient_white_border"
                    android:layout_marginRight="10dp" />

                <TextView
                    android:id="@+id/txt_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Pending"
                    android:textColor="@color/coloryellow"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:padding="10dp"
                    android:background="@drawable/bg_gredient_white_border" />

            </LinearLayout>

           <LinearLayout
               android:id="@+id/pay_load"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_marginTop="@dimen/dp10"
               android:visibility="gone"
               android:orientation="vertical">

               <TextView
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:layout_gravity="center_horizontal"
                   android:text="Select Payment Method"
                   android:textColor="@color/colordullwhite"
                   android:textSize="25dp"
                   android:textStyle="bold"
                   android:textAlignment="center"
                   android:background="@color/colorPrimary"
                   android:visibility="visible" />

               <RadioGroup
                   android:id="@+id/radioAppChoice"
                   android:layout_width="match_parent"
                   android:background="@color/white"
                   android:padding="@dimen/dp10"
                   android:layout_height="wrap_content">

                   <RadioButton
                       android:id="@+id/app_default"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:checked="true"
                       android:text="Default"
                       android:visibility="visible"/>

                   <RadioButton
                       android:id="@+id/app_amazonpay"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:visibility="visible"
                       android:text="AmazonPay" />

                   <RadioButton
                       android:id="@+id/app_bhim_upi"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:visibility="visible"
                       android:text="BHIM UPI" />

                   <RadioButton
                       android:id="@+id/app_google_pay"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:visibility="visible"
                       android:text="Google Pay" />

                   <RadioButton
                       android:id="@+id/app_phonepe"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:visibility="visible"
                       android:text="PhonePe" />

                   <RadioButton
                       android:id="@+id/app_paytm"
                       android:layout_width="wrap_content"
                       android:visibility="visible"
                       android:layout_height="wrap_content"
                       android:text="PayTm" />
               </RadioGroup>


           </LinearLayout>
            <ImageView
                android:id="@+id/imgPaynow"
                android:layout_width="200dp"
                android:layout_height="100dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:src="@drawable/paynow" />

            <TextView
                android:id="@+id/btnDummyPay"
                android:layout_width="200dp"
                android:layout_height="50dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/red_button"
                android:text="Dummy Pay now"
                android:gravity="center"
                android:paddingBottom="@dimen/dp5"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:visibility="gone"
                />


        </LinearLayout>

    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>