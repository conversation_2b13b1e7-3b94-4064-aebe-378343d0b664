<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    style="@style/dialogParentStyle"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        >

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            style="@style/popUpBoxbg"
            android:orientation="vertical"
            android:id="@+id/lnr_box"
            android:layout_marginTop="@dimen/pop_up_top_margin"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_marginHorizontal="25dp"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_marginVertical="42dp"
                android:layout_marginHorizontal="35dp"
                >

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recylerview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/btnconfirm"
                    android:layout_marginBottom="@dimen/dp5" />

                <Button
                    android:id="@+id/btnconfirm"
                    android:layout_width="@dimen/dimen_100dp"
                    android:layout_height="@dimen/dp30"
                    android:background="@drawable/ic_btn_confirm"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    />


            </RelativeLayout>

            <TextView
                android:id="@+id/txtnotfound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="Data no available!"
                android:gravity="center"
                android:visibility="gone"
                />

            <RelativeLayout
                android:id="@+id/rlt_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                >

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="300dp"
                    android:layout_height="100dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:indeterminateDrawable="@drawable/cpb_3"
                    android:indeterminateDuration="4000"
                    android:visibility="visible" />
            </RelativeLayout>





        </RelativeLayout>


        <include
            layout="@layout/dialog_toolbar"/>

    </RelativeLayout>



</RelativeLayout>