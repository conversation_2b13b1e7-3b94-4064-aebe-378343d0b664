// apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.android.library'

android {
    compileSdkVersion 35

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 35

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    android {
        compileOptions {
            sourceCompatibility 1.8
            targetCompatibility 1.8
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    ndkVersion '26.1.10909125'
    namespace 'com.first_player_games'

}
repositories {
    mavenCentral()
}

dependencies {

    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.firebase:firebase-auth:20.0.1'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'com.google.firebase:firebase-firestore:22.0.1'
    implementation 'com.google.firebase:firebase-database:19.6.0'
    implementation 'com.google.firebase:firebase-functions:19.2.0'
    implementation 'com.google.firebase:firebase-storage:19.2.1'
    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:30.2.0')
   // implementation 'com.google.firebase:firebase-core'
//    implementation('com.google.firebase:firebase-auth') {
//        exclude module: "play-services-safetynet"
//    }
    // Declare the dependency for the Analytics library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    testImplementation 'junit:junit:4.13'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation 'com.google.android.gms:play-services-auth:19.0.0'
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.2.0"
    implementation 'com.razorpay:checkout:1.5.16'
    implementation 'com.google.android.gms:play-services-ads:19.6.0'
//    implementation 'com.facebook.android:facebook-android-sdk:5.15.3'
//    implementation 'com.facebook.android:facebook-login:[8,9)'
    implementation ('io.socket:socket.io-client:2.0.0') {
        exclude group: 'org.json', module: 'json'
    }

    // CASHFREE libraries
    implementation'com.cashfree.pg:android-sdk:1.5.1'


    // Paytm libraries
    implementation 'com.paytm.appinvokesdk:appinvokesdk:1.6.7'


    implementation  "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation  "com.squareup.retrofit2:converter-gson:$gsonConverterVersion"
    implementation  "com.squareup.okhttp3:okhttp:$okhttpVersion"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.2.1'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.squareup.picasso:picasso:2.5.2'

}
//apply plugin: 'com.google.firebase.crashlytics'
