<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape xmlns:android="http://schemas.android.com/apk/res/android"
            android:shape="rectangle">
            <!-- This is the stroke you want to define -->
            <stroke
                android:width="2dp"
                android:color="@color/gold_color" />

            <!-- Optional, round your corners -->
            <corners
                android:bottomLeftRadius="@dimen/dp10"
                android:bottomRightRadius="@dimen/dp10"
                android:topLeftRadius="@dimen/dp10"
                android:topRightRadius="@dimen/dp10" />

            <!-- Optional, fill the rest of your background with a color or gradient, use transparent if you only want the border to be displayed-->
            <gradient
                android:angle="90"
                android:endColor="@color/purple_700"
                android:startColor="@color/purple" />
        </shape>
    </item>
    <item android:bottom="3dp">
        <shape android:shape="rectangle" >
            <corners
                android:bottomLeftRadius="5dp"
                android:bottomRightRadius="5dp"
                android:topLeftRadius="5dp"
                android:topRightRadius="5dp" />

            <solid android:color="#1b251c" />
        </shape>
    </item>
</layer-list>