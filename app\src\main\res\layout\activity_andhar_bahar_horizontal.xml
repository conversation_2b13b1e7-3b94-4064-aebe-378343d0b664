<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="0dp"
    android:paddingLeft="0dp"
    android:paddingRight="0dp"
    android:paddingTop="0dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="._AdharBahar.GameActivity"
    android:gravity="center"
    android:background="@drawable/home_bg2"
    >

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/back"
        android:visibility="visible" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <RelativeLayout
            android:id="@+id/rtlAmpire"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/imgTable"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="-100dp">

            <ImageView
                android:id="@+id/imgampire"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/poker_man"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/imgTip"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/tip"
                android:layout_marginTop="30dp"
                android:layout_marginLeft="-110dp"
                android:layout_toRightOf="@+id/imgampire"
                android:visibility="gone"/>


        </RelativeLayout>

        <!--// Also change from table image if changing-->
        <ImageView
            android:id="@+id/imgTable"
            android:layout_width="547dp"
            android:layout_height="310dp"
            android:layout_above="@+id/lnrtypegame"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="80dp"
            android:src="@drawable/table_v4"
            android:visibility="visible" />




        <Button
            android:id="@+id/bt_startgame"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/finish_btn"
            android:layout_gravity="center_vertical"
            android:text="Game start"
            android:textSize="11sp"
            android:layout_marginRight="50dp"
            android:layout_marginTop="50dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            />

        <RelativeLayout
            android:id="@+id/rltmaiCards"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="103dp"
            android:visibility="visible">

            <RelativeLayout
                android:id="@+id/ivallcard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="25dp">

                <ImageView
                    android:id="@+id/iv_jokercard"
                    android:layout_width="@dimen/ab_card_width"
                    android:layout_height="0dp"
                    android:src="@drawable/backside_card"
                    android:visibility="invisible" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rlt_centercard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btnandarpercent"
                        android:layout_width="50dp"
                        android:layout_height="22dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/blue"
                        android:text="66%"
                        android:visibility="gone" />

                    <Button
                        android:id="@+id/btnmaincardsvaluehiostory"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:background="@color/Golder_yellow"
                        android:text="6"
                        android:textColor="@color/black"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/imgmaincardsvaluehiostory"
                        android:layout_width="40dp"
                        android:layout_height="50dp"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:src="@drawable/bl4" />

                    <Button
                        android:id="@+id/btnbaharpercent"
                        android:layout_width="50dp"
                        android:layout_height="22dp"
                        android:layout_gravity="center_vertical"
                        android:background="#D50000"
                        android:text="66%"
                        android:visibility="gone" />
                </LinearLayout>
            </RelativeLayout>
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="15dp"
            android:paddingLeft="15dp"
            android:gravity="center"
            android:layout_marginLeft="40dp"
            android:layout_marginTop="10dp"
            >
            <TextView
                android:id="@+id/txtGameRunning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Please wait for Next Round"
                android:textColor="#EEC283"
                android:textSize="20dp"
                android:textStyle="bold"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:visibility="gone" />
            <TextView
                android:id="@+id/txtGameBets"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text="Place your coins"
                android:textColor="#EEC283"
                android:textSize="20dp"
                android:textStyle="bold"
                android:visibility="gone" />
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            >


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="30dp"
                android:visibility="gone">


                <TextView
                    android:id="@+id/txt_room"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:text="Room Id"
                    android:textColor="#EEC283"
                    android:textSize="12dp"
                    android:textStyle="bold"></TextView>

                <TextView
                    android:id="@+id/txt_gameId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/txt_room"
                    android:layout_marginLeft="6dp"
                    android:text="Game Id"
                    android:textColor="#EEC283"
                    android:textSize="12dp"></TextView>

                <TextView
                    android:id="@+id/txt_online"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/txt_gameId"
                    android:layout_marginLeft="6dp"
                    android:text="Online User 0"
                    android:textColor="#EEC283"
                    android:textSize="12dp"></TextView>
            </RelativeLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="20dp"
                android:visibility="gone"
                app:cardBackgroundColor="#FF9800"
                app:cardCornerRadius="5dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <TextView
                        android:id="@+id/txt_min_max"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Min-Max"
                        android:textColor="@color/white"
                        android:textSize="12dp"></TextView>


                    <TextView

                        android:id="@+id/txt_pairs"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"

                        android:layout_below="@id/txt_min_max"
                        android:text="Pairs+: 1 - 75"

                        android:textColor="@color/white"
                        android:textSize="12dp"></TextView>

                    <TextView

                        android:id="@+id/txt_many_cards"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"

                        android:layout_below="@id/txt_pairs"
                        android:text="6 Card: 1 - 5"

                        android:textColor="@color/white"
                        android:textSize="12dp"></TextView>

                </RelativeLayout>

            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:id="@+id/lnrpattecountoptions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="7dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">


                <LinearLayout
                    android:id="@+id/lnrfirstlince"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/lnrBallence"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/rlt1to5"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt1to5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="1-5(*3.5)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt6to10"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt6to10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="6-10(*4.5)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt11to15"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt11to15"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="11-15(*5.5)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt16to25"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt16to25"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="16-25(*4.5)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrsecondlince"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/lnrBallence"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="7dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/rlt26to30"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt26to30"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="26-30(*15)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt31to35"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt31to35"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="31-35(*25)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt36to40"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt36to40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="36-40(*50)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlt41more"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border">

                        <TextView
                            android:id="@+id/txt41more"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="41more(*120)"
                            android:textColor="#EEC283"
                            android:textSize="10dp" />
                    </RelativeLayout>
                </LinearLayout>

            </LinearLayout>


        </RelativeLayout>

        <LinearLayout
            android:id="@+id/lnrtypegame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="left|bottom"
                android:layout_weight="1"
                android:gravity="left|bottom">

                <HorizontalScrollView
                    android:id="@+id/scrollView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left|bottom"
                    android:gravity="left|bottom"
                    android:orientation="horizontal"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/lnrfollow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"></LinearLayout>
                </HorizontalScrollView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnranderbahar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/rltandarbet"
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_border"
                    android:padding="3dp">

                    <TextView
                        android:id="@+id/txtandar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="3dp"
                        android:ellipsize="end"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text="ANDAR"
                        android:textColor="#EEC283"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/txtandar"
                        android:layout_centerHorizontal="true"
                        android:text="*1.85" />

                    <RelativeLayout
                        android:id="@+id/rltmainviewander"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_alignLeft="@id/txt_catander"
                            android:layout_alignTop="@id/txt_catander"
                            android:layout_alignRight="@id/txt_catander"
                            android:layout_alignBottom="@id/txt_catander"
                            android:src="@drawable/circle" />

                        <TextView
                            android:id="@+id/txt_catander"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:minWidth="50dp"
                            android:minHeight="50dp"
                            android:padding="3dp"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:singleLine="true"
                            android:text="0"
                            android:textColor="@color/colorPrimary"
                            android:textSize="13sp"
                            android:textStyle="bold" />
                    </RelativeLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rltbaharbet"
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/background_border"
                    android:padding="3dp">

                    <TextView
                        android:id="@+id/txtBahar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="3dp"
                        android:ellipsize="end"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text="BAHAR"
                        android:textColor="#EEC283"
                        android:textSize="15dp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/txtBahar"
                        android:layout_centerHorizontal="true"
                        android:text="*1.95" />

                    <RelativeLayout
                        android:id="@+id/rltmainviewbahar"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/circle"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/txt_catbahar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:ellipsize="end"
                            android:padding="3dp"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:singleLine="true"
                            android:text="1"
                            android:textColor="@color/colorPrimary"
                            android:textSize="13sp"
                            android:textStyle="bold" />
                    </RelativeLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone">

                <Button
                    android:id="@+id/btnRepeat"
                    android:layout_width="@dimen/ab_button_width"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/yellow_button"
                    android:padding="5dp"
                    android:text="REPEAT"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/btnDouble"
                    android:layout_width="@dimen/ab_button_width"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/blue_button"
                    android:padding="5dp"
                    android:text="DOUBLE"
                    android:textSize="12dp"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lnrButtons"
            android:layout_above="@+id/lnrtypegame"
            android:layout_alignParentRight="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_10dp"
            android:layout_marginRight="@dimen/dimen_10dp"
            >
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            >

            <Button
                android:visibility="visible"
                android:id="@+id/btnconfirm"
                android:layout_width="@dimen/ab_button_width"
                android:layout_height="30dp"
                android:padding="5dp"
                android:textSize="12dp"
                android:background="@drawable/ic_btn_confirm"
                android:layout_marginRight="10dp"
                />

        </LinearLayout>

        <Button
            android:id="@+id/btnCANCEL"
            android:layout_width="@dimen/ab_button_width"
            android:layout_height="30dp"
            android:textSize="12dp"
            android:background="@drawable/ic_btn_cancel"
            android:layout_above="@+id/lnrtypegame"
            android:layout_alignParentRight="true"
            />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rlt_cards"
            android:layout_width="450dp"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:layout_below="@+id/rltmaiCards"
            android:orientation="horizontal"
            android:visibility="visible">


            <LinearLayout
                android:id="@+id/lnrGameCardsView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|right"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_horizontal">

                    <ImageView
                        android:id="@+id/ivDragonCard"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="@dimen/ab_card_hight"
                        android:src="@drawable/backside_card"
                        android:visibility="visible" />

                    <HorizontalScrollView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                    <RelativeLayout
                        android:id="@+id/lnrandarpatte"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="@dimen/ab_card_hight"
                        android:orientation="horizontal" />
                    </HorizontalScrollView>

                    <TextView
                        android:id="@+id/tvbottomSpace"
                        style="@style/ShadowWhiteTextview"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ivDragonCard"
                        android:gravity="center"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text="Andhar"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_horizontal">

                    <ImageView
                        android:id="@+id/ivTigerCard"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="@dimen/ab_card_width"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:src="@drawable/backside_card" />

                    <RelativeLayout
                        android:id="@+id/lnrbaharpatte"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="@dimen/ab_card_hight"
                        android:layout_marginLeft="3dp"
                        android:layout_marginRight="3dp"
                        android:orientation="horizontal" />

                    <TextView
                        android:id="@+id/tvbottomSpace1"
                        style="@style/ShadowWhiteTextview"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ivTigerCard"
                        android:gravity="center"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text="Bahar"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/rlt_centercard"
                    android:layout_weight="1">

                    <ImageView
                        android:id="@+id/ivGadhi"
                        android:layout_width="@dimen/ab_card_width"
                        android:layout_height="@dimen/ab_card_width"
                        android:layout_centerHorizontal="true"
                        android:src="@drawable/backside_card" />


                    <TextView
                        android:id="@+id/tvStartTimer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ivGadhi"
                        android:layout_centerHorizontal="true"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text=""
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:visibility="visible" />

                </RelativeLayout>


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right|center_vertical">

                <ImageView
                    android:id="@+id/imgCardsandar"
                    android:layout_width="@dimen/ab_card_width"
                    android:layout_height="@dimen/ab_card_hight"
                    android:layout_marginRight="33dp"
                    android:src="@drawable/backside_card"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/imgCardsbahar"
                    android:layout_width="@dimen/ab_card_width"
                    android:layout_height="@dimen/ab_card_hight"
                    android:layout_marginRight="23dp"
                    android:src="@drawable/backside_card"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/animationspace"
                    android:layout_width="@dimen/ab_card_width"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/imgCardsandar"
                    android:gravity="center"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:textColor="@color/white"
                    android:textSize="18sp" />
            </RelativeLayout>


        </RelativeLayout>



        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="125dp"
            android:layout_below="@+id/rlt_cards"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="50dp"
            android:visibility="gone"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="110dp"
                android:layout_marginLeft="1dp"
                android:layout_marginRight="5dp"
                android:layout_gravity="center"
                android:gravity="center"
                >

                <ImageView
                    android:id="@+id/imgmaincard"
                    android:layout_width="25dp"
                    android:layout_height="35dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/bl4"
                    android:visibility="gone"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ANDAR"
                    android:textSize="16dp"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:ellipsize="end"
                    android:shadowRadius="3"
                    android:textColor="#EEC283"
                    android:layout_marginRight="20dp"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="BAHAR"
                    android:textSize="16dp"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentRight="true"
                    android:ellipsize="end"
                    android:shadowRadius="3"
                    android:textColor="#EEC283"/>

                <RelativeLayout
                    android:layout_toRightOf="@+id/imgmaincard"
                    android:id="@+id/lnrpaate"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="1dp"
                    >

                    <RelativeLayout
                        android:id="@+id/rltline"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_centerVertical="true"
                        android:background="#EEC283"></RelativeLayout>

                </RelativeLayout>

            </LinearLayout>
        </HorizontalScrollView>


    </RelativeLayout>

    <!--  Player 1 START -->


    <RelativeLayout
        android:id="@+id/rltplayer1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20dp"
        android:layout_marginTop="15dp"
        >


        <RelativeLayout
            android:id="@+id/rltcirclproimage1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/imgpl1glow"
                android:layout_width="@dimen/Player1_width"
                android:layout_height="@dimen/Player1_height"
                android:layout_centerInParent="true"
                android:src="@drawable/glow_circle"
                android:visibility="gone" />

            <RelativeLayout
                android:layout_width="@dimen/Player1_bg_width"
                android:layout_height="@dimen/Player1_bg_height"
                android:layout_centerInParent="true"
                android:background="@drawable/user_bg_circle" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/imgpl1circle"
                android:layout_width="@dimen/Player1_circle_width"
                android:layout_height="@dimen/Player2_circle_height"
                android:layout_centerInParent="true"
                android:src="@drawable/avatar"
                android:visibility="visible" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_toRightOf="@+id/rltcirclproimage1"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="10dp"
            >

            <TextView
                android:id="@+id/txtName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="3"
                android:text="asif"
                android:textColor="@color/white"
                android:textSize="13dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/txt_gameId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="3"
                android:text="asif"
                android:textSize="13dp"
                android:textStyle="bold"
                android:visibility="gone"
                />

            <TextView
                android:id="@+id/txtBallence"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="3"
                android:text=""
                android:textSize="13dp"
                android:textStyle="bold" />

        </LinearLayout>

    </RelativeLayout>

    <!--  Player 1 END -->

    <RelativeLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="#90000000"
        >


        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/imgpl1circle"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerInParent="true"
            android:src="@drawable/avatar"
            android:visibility="visible"
            />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="-30dp"
            android:layout_above="@+id/imgpl1circle"
            >
            <ImageView
                android:layout_width="120dp"
                android:layout_height="80dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/giphy"
                android:visibility="visible"
                />

            <ImageView
                android:layout_width="120dp"
                android:layout_height="80dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/star"
                android:visibility="visible"  />


        </RelativeLayout>


    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:background="#90000000"
        android:visibility="gone"
        android:gravity="center"
        >


        <ImageView
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            android:src="@drawable/star"
            android:visibility="visible"  />

        <ImageView
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            android:src="@drawable/ic_lose"
            android:visibility="visible"
            />


    </RelativeLayout>


</RelativeLayout>