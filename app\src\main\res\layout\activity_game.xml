<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/activity_main"
    android:background="@drawable/iv_building"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/blue"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:background="#3E2723"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:orientation="horizontal">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:orientation="horizontal"
                        android:layout_marginTop="5dp">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginTop="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:orientation="horizontal">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginTop="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:orientation="horizontal">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginTop="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:orientation="horizontal">
                        <Button
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />
                        <Button
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="15dp"
                            android:background="@color/red"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="2dp"
                            />

                    </LinearLayout>

                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:orientation="horizontal"
                    android:layout_alignParentBottom="true">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_centerInParent="true">
                        <Button
                            android:id="@+id/btnandarpercent"
                            android:layout_width="50dp"
                            android:layout_height="22dp"
                            android:background="@color/blue"
                            android:layout_gravity="center_vertical"
                            android:text="66%"/>
                        <Button
                            android:id="@+id/btnmaincardsvaluehiostory"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:background="@color/Golder_yellow"
                            android:textColor="@color/black"
                            android:text="6"
                            android:visibility="gone"
                            android:layout_marginLeft="3dp"
                            android:layout_marginRight="3dp"/>

                        <ImageView
                            android:id="@+id/imgmaincardsvaluehiostory"
                            android:layout_width="23dp"
                            android:layout_height="28dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/bl4"
                            android:layout_marginLeft="3dp"
                            android:layout_marginRight="3dp"/>


                        <Button
                            android:id="@+id/btnbaharpercent"
                            android:layout_width="50dp"
                            android:layout_height="22dp"
                            android:layout_gravity="center_vertical"
                            android:background="#D50000"
                            android:text="66%" />
                    </LinearLayout>


                </RelativeLayout>

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginLeft="5dp"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="46dp"
                            android:layout_height="56dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="28dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginLeft="5dp"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="30dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginLeft="5dp"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_marginRight="4dp"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:layout_gravity="bottom"
                            android:layout_marginRight="4dp"
                            android:background="@color/blue">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="25dp"
                            android:layout_height="30dp"
                            android:background="@color/red"
                            android:layout_gravity="bottom">
                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="22dp"
                                android:src="@drawable/bl4"
                                android:layout_centerInParent="true"
                                android:layout_marginLeft="3dp"
                                android:layout_marginRight="3dp"/>
                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:orientation="horizontal"
                    android:layout_alignParentBottom="true">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_centerInParent="true">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="10dp"
                            android:textColor="@color/Golder_yellow"
                            android:layout_gravity="center_vertical"
                            android:text="ANDAR"/>
                        <Button
                            android:layout_width="50dp"
                            android:layout_height="22dp"
                            android:background="@color/blue"
                            android:textColor="@color/white"
                            android:text="6"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="3dp"
                            android:layout_marginRight="3dp"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="10dp"
                            android:layout_gravity="center_vertical"
                            android:textColor="@color/Golder_yellow"
                            android:text="BAHAR"
                            android:layout_marginRight="3dp"/>
                        <Button
                            android:layout_width="50dp"
                            android:layout_height="22dp"
                            android:layout_gravity="center_vertical"
                            android:background="#D50000"
                            android:text="66%"/>
                    </LinearLayout>


                </RelativeLayout>
            </RelativeLayout>


        </LinearLayout>

        <RelativeLayout
            android:layout_marginTop="10dp"
            android:background="@drawable/girl_bg"
            android:layout_below="@id/blue"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:layout_above="@+id/rltmaingame"
                android:id="@+id/imgCardsandar"
                android:layout_centerHorizontal="true"
                android:src="@drawable/backside_card"
                android:layout_width="25dp"
                android:layout_marginBottom="55dp"
                android:visibility="visible"
                android:layout_height="30dp"></ImageView>
            <ImageView
                android:layout_above="@+id/rltmaingame"
                android:id="@+id/imgCardsbahar"
                android:layout_centerHorizontal="true"
                android:src="@drawable/backside_card"
                android:layout_width="25dp"
                android:layout_marginBottom="55dp"
                android:visibility="visible"
                android:layout_height="30dp"></ImageView>

            <RelativeLayout
                android:layout_marginBottom="30dp"
                android:layout_above="@+id/rltmaingame"
                android:layout_width="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/txt_room"
                    android:text="Room Id"
                    android:textColor="#EEC283"
                    android:textSize="12dp"
                    android:textStyle="bold"
                    android:layout_marginLeft="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>
                <TextView
                    android:layout_below="@id/txt_room"
                    android:id="@+id/txt_gameId"
                    android:text="Game Id"
                    android:layout_marginLeft="6dp"
                    android:textColor="#EEC283"
                    android:textSize="12dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>

                <TextView
                    android:layout_below="@id/txt_gameId"
                    android:id="@+id/txt_online"
                    android:text="Online User 0"
                    android:layout_marginLeft="6dp"
                    android:textColor="#EEC283"
                    android:textSize="12dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>
            </RelativeLayout>
            <RelativeLayout
                android:layout_above="@+id/rltmaingame"
                android:layout_marginBottom="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp">

                <TextView
                    android:id="@+id/txt_min_max"

                    android:text="Min-Max"

                    android:textColor="#EEC283"
                    android:textSize="12dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>



                <TextView

                    android:layout_below="@id/txt_min_max"
                    android:id="@+id/txt_pairs"
                    android:text="Pairs+: 1 - 75"

                    android:textColor="#EEC283"
                    android:textSize="12dp"

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>

                <TextView

                    android:layout_below="@id/txt_pairs"
                    android:id="@+id/txt_many_cards"
                    android:text="6 Card: 1 - 5"

                    android:textColor="#EEC283"
                    android:textSize="12dp"

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"></TextView>

            </RelativeLayout>
            <TextView
                android:id="@+id/txtGameRunning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Please wait for Next Round"
                android:textColor="#EEC283"
                android:textSize="20dp"
                android:textStyle="bold"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:visibility="gone" />
            <TextView
                android:id="@+id/txtGameBets"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:text="Place your coins"
                android:textColor="#EEC283"
                android:textSize="20dp"
                android:textStyle="bold"

                android:visibility="gone" />

            <ImageView
                android:id="@+id/bl4"
                android:layout_width="30dp"
                android:layout_height="40dp"
                android:src="@drawable/bl4"
                android:layout_marginTop="190dp"
                android:visibility="gone"
                android:layout_centerHorizontal="true"/>

            <RelativeLayout
                android:layout_above="@+id/rltmaingame"
                android:id="@+id/rltGameFinish"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="110dp"
                android:layout_marginBottom="-20dp"
                >
                <TextView
                    android:id="@+id/tvStartTimer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text=""
                    android:textColor="#FFB600"
                    android:textSize="35dp"
                    android:textStyle="bold"
                    android:visibility="visible" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltmaingame"
                android:layout_width="match_parent"
                android:layout_height="110dp"
                android:layout_marginLeft="1dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                android:layout_above="@id/lnrpattecountoptions">

                <ImageView
                    android:id="@+id/imgmaincard"
                    android:layout_width="25dp"
                    android:layout_height="35dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/bl4"/>

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ANDAR"
                    android:textSize="16dp"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:layout_alignParentRight="true"
                    android:shadowDy="1"
                    android:ellipsize="end"
                    android:shadowRadius="3"

                    android:textColor="#EEC283"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="BAHAR"
                    android:textSize="16dp"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentRight="true"
                    android:ellipsize="end"
                    android:shadowRadius="3"
                    android:layout_marginBottom="10dp"
                    android:textColor="#EEC283"/>

                <RelativeLayout
                    android:layout_toRightOf="@+id/imgmaincard"
                    android:id="@+id/lnrpaate"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="1dp"
                    >

                    <LinearLayout
                        android:id="@+id/lnrandarpatte"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:orientation="horizontal"
                        >

                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/rltline"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_centerVertical="true"
                        android:background="#EEC283"></RelativeLayout>

                    <LinearLayout
                        android:id="@+id/lnrbaharpatte"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_alignParentBottom="true"
                        android:orientation="horizontal">

                    </LinearLayout>

                </RelativeLayout>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/lnrpattecountoptions"
                android:layout_above="@id/lnranderbahar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:layout_marginBottom="7dp"
                android:orientation="vertical">



                <LinearLayout
                    android:id="@+id/lnrfirstlince"
                    android:layout_above="@+id/lnrBallence"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <RelativeLayout
                        android:id="@+id/rlt1to5"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt1to5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1-5"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt6to10"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt6to10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="6-10"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt11to15"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt11to15"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="11-15"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt16to25"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt16to25"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="16-25"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="7dp"
                    android:id="@+id/lnrsecondlince"
                    android:layout_above="@+id/lnrBallence"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <RelativeLayout
                        android:id="@+id/rlt26to30"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt26to30"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="26-30"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt31to35"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt31to35"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="31-35"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt36to40"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt36to40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="36-40"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/rlt41more"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="10dp"
                        android:background="@drawable/background_border"
                        >
                        <TextView
                            android:id="@+id/txt41more"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="41more"
                            android:textSize="10dp"
                            android:layout_marginTop="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:layout_marginLeft="10dp"
                            android:textColor="#EEC283"/>
                    </RelativeLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnranderbahar"
                android:layout_above="@+id/lnrBallence"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/rltandarbet"
                    android:layout_width="160dp"
                    android:layout_marginLeft="5dp"
                    android:layout_height="wrap_content"
                    android:padding="3dp"
                    android:background="@drawable/background_border"
                    >
                    <TextView
                        android:id="@+id/txtandar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ANDAR"
                        android:textSize="15dp"
                        android:textStyle="bold"
                        android:layout_marginTop="3dp"
                        android:layout_centerHorizontal="true"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:ellipsize="end"
                        android:shadowRadius="3"
                        android:layout_marginLeft="10dp"
                        android:textColor="#EEC283"/>
                    <TextView
                        android:layout_below="@+id/txtandar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="*1.85"
                        android:layout_centerHorizontal="true"/>

                    <RelativeLayout
                        android:id="@+id/rltmainviewander"
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/circle"
                        android:visibility="gone"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1"
                            android:id="@+id/txt_catander"
                            android:textSize="14dp"
                            android:textStyle="bold"
                            android:padding="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:textColor="@color/colorPrimary"/>

                    </RelativeLayout>

                </RelativeLayout>
                <RelativeLayout
                    android:id="@+id/rltbaharbet"
                    android:layout_width="160dp"
                    android:layout_height="wrap_content"
                    android:padding="3dp"
                    android:background="@drawable/background_border"
                    android:layout_marginLeft="10dp"
                    >

                    <TextView
                        android:id="@+id/txtBahar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="3dp"
                        android:ellipsize="end"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:text="BAHAR"
                        android:textColor="#EEC283"
                        android:textSize="15dp"
                        android:textStyle="bold" />
                    <TextView
                        android:layout_below="@+id/txtBahar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="*1.95"
                        android:layout_centerHorizontal="true"/>

                    <RelativeLayout
                        android:id="@+id/rltmainviewbahar"
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:visibility="gone"
                        android:background="@drawable/circle"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1"
                            android:id="@+id/txt_catbahar"
                            android:textSize="14dp"
                            android:textStyle="bold"
                            android:padding="3dp"
                            android:layout_centerInParent="true"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:shadowRadius="3"
                            android:textColor="@color/colorPrimary"/>

                    </RelativeLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrBallence"
                android:layout_above="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="27dp"
                android:layout_centerHorizontal="true"
                android:gravity="center"

                android:orientation="horizontal">
                <TextView
                    android:id="@+id/txtName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textSize="13dp"
                    android:textStyle="bold"
                    android:padding="5dp"

                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:ellipsize="end"
                    android:shadowRadius="3"
                    android:layout_marginLeft="10dp"
                    android:textColor="#EEC283"/>
                <TextView
                    android:id="@+id/txtBallence"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textSize="13dp"
                    android:textStyle="bold"
                    android:padding="5dp"

                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:layout_marginLeft="10dp"
                    android:ellipsize="end"
                    android:shadowRadius="3"
                    android:textColor="#EEC283"/>
            </LinearLayout>

            <HorizontalScrollView
                android:id="@+id/scrollView"
                android:layout_above="@+id/lnrtypegame"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:scrollbars="none"
                android:layout_marginBottom="10dp"
                android:layout_marginRight="5dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/lnrfollow"
                    android:orientation="horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <!--                    <RelativeLayout-->
                    <!--                        android:id="@+id/rltmainview"-->
                    <!--                        android:layout_width="60dp"-->
                    <!--                        android:layout_height="60dp"-->
                    <!--                        android:background="@drawable/circle"-->
                    <!--                        android:layout_marginLeft="10dp">-->
                    <!--                        <TextView-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:text="text"-->
                    <!--                            android:id="@+id/txt_cat"-->
                    <!--                            android:textSize="17dp"-->
                    <!--                            android:textStyle="bold"-->
                    <!--                            android:padding="5dp"-->
                    <!--                            android:layout_centerInParent="true"-->
                    <!--                            android:shadowColor="@color/black"-->
                    <!--                            android:shadowDx="1"-->
                    <!--                            android:shadowDy="1"-->
                    <!--                            android:ellipsize="end"-->
                    <!--                            android:shadowRadius="3"-->
                    <!--                            android:textColor="@color/colorPrimary"/>-->
                    <!--                    </RelativeLayout>-->
                </LinearLayout>

            </HorizontalScrollView>

            <LinearLayout
                android:id="@+id/lnrtypegame"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:visibility="visible"
                android:layout_marginBottom="5dp"
                android:weightSum="4"
                android:orientation="horizontal">
                <Button

                    android:id="@+id/btnRepeat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="REPEAT"
                    android:padding="5dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="5dp"
                    android:textSize="12dp"
                    android:background="@drawable/yellow_button"/>
                <Button
                    android:visibility="visible"
                    android:id="@+id/btnDouble"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="DOUBLE"
                    android:padding="5dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="5dp"
                    android:textSize="12dp"
                    android:background="@drawable/blue_button"/>
                <Button
                    android:visibility="visible"
                    android:id="@+id/btnconfirm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="CONFIRM"
                    android:padding="5dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="5dp"
                    android:textSize="12dp"
                    android:background="@drawable/yellow_dark_button"/>
                <Button

                    android:id="@+id/btnCANCEL"
                    android:layout_width="wrap_content"
                    android:layout_height="45dp"
                    android:text="CANCEL"
                    android:paddingBottom="5dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="5dp"
                    android:textSize="12dp"
                    android:background="@drawable/red_button"/>

            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="#90000000"
        >


        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/imgpl1circle"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerInParent="true"
            android:src="@drawable/avatar"
            android:visibility="visible"
            />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="-30dp"
            android:layout_above="@+id/imgpl1circle"
           >
            <ImageView
                android:layout_width="120dp"
                android:layout_height="80dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/giphy"
                android:visibility="visible"
                />

            <ImageView
                android:layout_width="120dp"
                android:layout_height="80dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/star"
                android:visibility="visible"  />


        </RelativeLayout>


    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:layout_above="@+id/rltplayer1"
        android:background="#90000000"
        android:visibility="gone"
        android:gravity="center"
        >


        <ImageView
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            android:src="@drawable/star"
            android:visibility="visible"  />

        <ImageView
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            android:src="@drawable/ic_lose"
            android:visibility="visible"
            />


    </RelativeLayout>



</RelativeLayout>
