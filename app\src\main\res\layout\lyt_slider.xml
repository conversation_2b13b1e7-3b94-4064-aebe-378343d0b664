<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="100dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/lyt_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="@dimen/spacing_medium"
        android:clipToPadding="false"
        android:visibility="visible"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="4dp"
        app:cardElevation="1dp"
        app:cardUseCompatPadding="false">
        <LinearLayout
            android:id="@+id/left_lyt_banner"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_above="@+id/rltBottom"

            android:layout_toRightOf="@+id/left_lyt"
            android:orientation="vertical"
            app:cardCornerRadius="20dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.viewpager.widget.ViewPager
                    android:id="@+id/viewpager"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/rel"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                  >

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="1dp">

                      <!--  <cn.trinea.android.view.autoscrollviewpager.AutoScrollViewPager
                            android:id="@+id/pager"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                        <com.gigamole.infinitecycleviewpager.HorizontalInfiniteCycleViewPager
                            android:id="@+id/view_pager"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="gone"
                            app:icvp_center_page_scale_offset="30dp"
                            app:icvp_interpolator="@android:anim/accelerate_interpolator"
                            app:icvp_max_page_scale="0.8"
                            app:icvp_min_page_scale="0.55"
                            app:icvp_min_page_scale_offset="5dp"
                            app:icvp_scroll_duration="500" />-->

                    </androidx.cardview.widget.CardView>
                </RelativeLayout>

                <com.viewpagerindicator.CirclePageIndicator
                    android:id="@+id/titles"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rel"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="-40dp"
                    app:fillColor="@color/colorPrimary"
                    app:strokeColor="@color/colorPrimary" />

            </RelativeLayout>
        </LinearLayout>

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="vertical"-->
<!--            android:visibility="gone"-->
<!--            >-->

<!--            <RelativeLayout-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="0dp"-->
<!--                android:layout_weight="1">-->

<!--                <androidx.viewpager.widget.ViewPager-->
<!--                    android:id="@+id/pager"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="@dimen/_180sdp"-->
<!--                    android:layout_alignParentStart="true"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    android:layout_alignParentTop="true"-->
<!--                    android:background="@color/grey_20" />-->

<!--                <ImageButton-->
<!--                    android:id="@+id/bt_previous"-->
<!--                    android:layout_width="@dimen/spacing_mlarge"-->
<!--                    android:layout_height="@dimen/spacing_xlarge"-->
<!--                    android:layout_alignParentLeft="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:background="@color/darkOverlaySoft"-->
<!--                    android:rotation="180"-->
<!--                    android:src="@drawable/ic_arrow_right" />-->

<!--                <ImageButton-->
<!--                    android:id="@+id/bt_next"-->
<!--                    android:layout_width="@dimen/spacing_mlarge"-->
<!--                    android:layout_height="@dimen/spacing_xlarge"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:background="@color/darkOverlaySoft"-->
<!--                    android:src="@drawable/ic_arrow_right" />-->

<!--            </RelativeLayout>-->

<!--            <View-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="1px"-->
<!--                android:background="@color/grey_10" />-->

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:background="@android:color/white"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:padding="@dimen/spacing_large"-->
<!--                android:visibility="gone">-->

<!--                <TextView-->
<!--                    android:id="@+id/featured_news_title"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:maxLines="1"-->
<!--                    android:singleLine="true"-->
<!--                    android:textAppearance="@style/Base.TextAppearance.AppCompat.Small"-->
<!--                    android:textColor="@color/colorPrimaryDark" />-->

<!--                <LinearLayout-->
<!--                    android:id="@+id/layout_dots"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_marginLeft="@dimen/spacing_large"-->
<!--                    android:gravity="end|center_vertical"-->
<!--                    android:orientation="horizontal" />-->

<!--            </LinearLayout>-->
<!--        </LinearLayout-->
<!--            -->
<!--            >-->

    </androidx.cardview.widget.CardView>

</RelativeLayout>
