<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp">

    <RelativeLayout
        android:layout_width="350dp"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_55dp"
                android:layout_marginBottom="@dimen/dp5"
                android:paddingLeft="10dp"
                android:paddingTop="8dp"
                android:paddingRight="10dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_width="@dimen/dp35"
                    android:layout_height="match_parent"
                    android:src="@drawable/app_logo_second" />

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:paddingLeft="@dimen/dp10"
                    android:paddingTop="@dimen/dp7"
                    android:text="@string/app_name"
                    android:textAllCaps="true"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    app:fontFilePath="@string/Helvetica_Bold_Extra" />
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/btn_yes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/dp30"
                android:layout_marginRight="@dimen/dp30"
                android:layout_marginBottom="@dimen/dp10"
                android:background="@color/Golder_yellow"
                android:visibility="visible"
                app:cardCornerRadius="5dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/payu_dimen_45dp"
                    android:background="@drawable/all_players_pannel"
                    android:paddingLeft="20dp"
                    android:gravity="center"
                    android:paddingTop="8dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="8dp">

                    <TextView
                        android:id="@+id/tv_update"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="Update"
                        android:textAllCaps="true"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        app:fontFilePath="@string/Helvetica_Bold_Extra" />

                </RelativeLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>