<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    tools:context=".Activity.Splashscreen">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rl_extra"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/triangle"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp30"
                android:layout_centerHorizontal="true"
                android:layout_above="@id/wheel"
                android:tint="@color/blue"
                android:layout_alignParentTop="true"
                android:layout_marginBottom="-30dp"
                app:srcCompat="@drawable/triangle"
                tools:ignore="UseAppTint" />

            <ImageView
                android:id="@+id/wheel"
                android:layout_width="@dimen/payu_dimen_200dp"
                android:layout_height="@dimen/payu_dimen_200dp"
                android:layout_alignParentTop="true"
                android:layout_centerInParent="true"
                android:layout_marginTop="@dimen/dp25"
                android:adjustViewBounds="true"
                android:rotation="-13"
                android:scaleType="centerInside"
                app:srcCompat="@drawable/wheel_discount" />

            <TextView
                android:id="@+id/resultTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/spinBtn"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:text="you will get 2% discount"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/Golder_yellow"
                android:textSize="@dimen/payu_dimen_20sp"
                android:textStyle="bold"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/spin_txt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/wheel"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:text="You can spin only after every 10 minutes"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_14dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <Button
                android:id="@+id/spinBtn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:layout_below="@id/spin_txt"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_gredient_white_border"
                android:text="SPIN"
                android:textColor="@color/white" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/imgback"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/back"
            android:visibility="visible" />

        <!-- Main Content Container with Instructions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="10">

            <!-- Left Side: Wallet/QR Code Section -->

            <!-- Right Side: QR Code Section -->

            <LinearLayout
                android:id="@+id/qr"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp10"
                android:layout_weight="6"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtChipsdetails"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="Add Funds to Your Wallet"
                    android:textColor="@color/coloryellow"
                    android:textSize="24dp"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="Fast and secure payment gateway"
                    android:textColor="@color/colordullwhite"
                    android:textSize="16dp"
                    android:visibility="visible" />

                <!-- Address Field with Copy Icon -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp10"
                    android:background="@drawable/bg_gredient_white_border"
                    android:gravity="center_vertical"
                    android:minHeight="35dp"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <TextView
                        android:id="@+id/txt_address"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="middle"
                        android:maxLines="2"
                        android:padding="5dp"
                        android:text="Generating Wallet..."
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                    <ImageView
                        android:id="@+id/img_copy_address"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginLeft="10dp"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:padding="4dp"
                        android:src="@drawable/txtcopy" />

                </LinearLayout>

                <!-- Check Status Button and Status Display -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp5"
                    android:baselineAligned="false"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_check_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/bg_gredient_white_border"
                        android:minHeight="35dp"
                        android:padding="@dimen/dp10"
                        android:text="Check Status"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txt_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_gredient_white_border"
                        android:gravity="center"
                        android:padding="10dp"
                        android:text="Pending"
                        android:textColor="@color/coloryellow"
                        android:textSize="14dp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- View History Button -->
                <Button
                    android:id="@+id/btn_view_history"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/bg_gredient_white_border"
                    android:minHeight="35dp"
                    android:padding="@dimen/dp10"
                    android:text="View History"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <!-- QR Code Container for USDT payments -->
                <RelativeLayout
                    android:id="@+id/qr_container"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_margin="@dimen/dp10"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:padding="@dimen/dp10"
                    android:visibility="gone">

                    <!-- QR Code ImageView -->
                    <ImageView
                        android:id="@+id/qrcode"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerInside" />

                    <!-- USDT Logo in center -->
                    <ImageView
                        android:id="@+id/usdt"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_centerInParent="true"
                        android:background="@color/white"
                        android:padding="8dp"
                        android:scaleType="centerInside"
                        android:src="@drawable/usdt"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@color/colorPrimary"
                    android:gravity="center"
                    android:padding="10dp"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:visibility="gone" />

                <!-- Copy Address Button -->
                <TextView
                    android:id="@+id/copyaddress"
                    android:layout_width="150dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:background="@color/colorPrimary"
                    android:gravity="center"
                    android:padding="8dp"
                    android:text="Copy Address"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    android:visibility="gone" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp10"
                android:layout_weight="4"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="@dimen/dp10"
                    android:gravity="center"
                    android:text="Scan QR Code"
                    android:textColor="@color/coloryellow"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- QR Code ImageView -->

                <ImageView
                    android:id="@+id/qr_code_image"
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:layout_gravity="center"
                    android:background="@color/white"
                    android:scaleType="fitCenter" />

                <TextView
                    android:id="@+id/txt_qr_instruction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:text="Scan this QR code with your wallet"
                    android:textColor="@color/colordullwhite"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>