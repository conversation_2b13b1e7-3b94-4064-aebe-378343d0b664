<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardUseCompatPadding="true"
    app:cardCornerRadius="3dp"
    android:foreground="?selectableItemBackground"
    android:focusable="true"
    android:clickable="true"
    android:layout_marginTop="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:background="@color/white"
            android:orientation="vertical"
            >


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                >



                <TextView
                    android:id="@+id/txtname"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/name"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:textColor="#B3000000"
                    android:layout_marginLeft="6dp"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="10dp"
                >

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/common_full_open_on_phone"
                    android:tint="@color/black"
                    />

                <TextView
                    android:id="@+id/txtmobile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    />


            </LinearLayout>


        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?dividerHorizontal"
            />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="right"
            android:padding="5dp"
            >




        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>