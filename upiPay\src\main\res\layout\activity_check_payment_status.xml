<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".CheckPaymentStatus">

    <androidx.core.widget.NestedScrollView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/toolbr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_bg"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/colorPrimary"
                    android:backgroundTint="@color/colorPrimary"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/bt_menu"
                        android:layout_width="?attr/actionBarSize"
                        android:layout_height="?attr/actionBarSize"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="20dp"
                        app:srcCompat="@drawable/ic_back_arrow"
                        app:tint="@android:color/white" />

                    <TextView
                        android:id="@+id/toolbr_lbl"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="sans-serif"
                        android:gravity="center_vertical"
                        android:text="@string/app_name"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/upload"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:text="Upload"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                tools:context=".Activity.AllPaymentActivity">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/lyTop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:background="@color/colorPrimary"
                        android:minHeight="50dp"
                        android:orientation="vertical"
                        android:paddingStart="15dp"
                        android:paddingTop="15dp"
                        android:paddingEnd="15dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txn_id"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:text="Transaction Id: "
                                android:textColor="@color/white"
                                android:textSize="16sp"
                                android:visibility="visible" />


                        </LinearLayout>


                        <TextView
                            android:id="@+id/txt_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginStart="3dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:gravity="center"
                            android:text="Amount:"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <!--Main Content-->
                    <androidx.core.widget.NestedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@+id/lyTop"
                        android:fillViewport="true">

                        <!--Details-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="@dimen/spacing_smlarge"
                                    android:layout_marginLeft="@dimen/spacing_smlarge"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:id="@+id/view_1"
                                        android:layout_width="2dp"
                                        android:layout_height="match_parent"
                                        android:layout_centerHorizontal="true"
                                        android:background="@color/gray_line"
                                        android:orientation="vertical" />

                                    <ImageView
                                        android:id="@+id/tick_1"
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        app:srcCompat="@drawable/circle"
                                        app:tint="@color/gray_line" />

                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/card_1"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/spacing_middle"
                                    android:layout_marginRight="@dimen/spacing_middle"
                                    android:layout_marginBottom="@dimen/spacing_medium"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    app:cardCornerRadius="2dp"
                                    app:cardElevation="1dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:fontFamily="sans-serif"
                                        android:text="@string/go_to_payment_mobile_app"
                                        android:textColor="@color/black"
                                        android:textSize="18sp" />

                                    <LinearLayout
                                        android:id="@+id/upload_img"
                                        android:layout_width="150dp"
                                        android:layout_height="100dp"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginTop="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:id="@+id/image_"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:src="@drawable/payment_karo"
                                            android:visibility="visible" />

                                    </LinearLayout>

                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="@dimen/spacing_smlarge"
                                    android:layout_marginLeft="@dimen/spacing_smlarge"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">


                                    <ImageView
                                        android:id="@+id/tick_2"
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        app:srcCompat="@drawable/circle"
                                        app:tint="@color/gray_line" />
                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/card_2"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/spacing_middle"
                                    android:layout_marginRight="@dimen/spacing_middle"
                                    android:layout_marginBottom="@dimen/spacing_medium"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    app:cardCornerRadius="2dp"
                                    app:cardElevation="1dp">

                                    <!--                                    old-->
                                    <!--                                    <TextView-->
                                    <!--                                        android:layout_width="match_parent"-->
                                    <!--                                        android:layout_height="wrap_content"-->
                                    <!--                                        android:fontFamily="sans-serif"-->
                                    <!--                                        android:text="@string/check_the_pending_requests_and_approve_payment_by_entering_upi_pin"-->
                                    <!--                                        android:textColor="@color/black"-->
                                    <!--                                        android:textSize="18sp" />-->
                                    <!--                                    new-->
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginBottom="15dp"
                                        android:fontFamily="sans-serif"
                                        android:text="@string/use_step"
                                        android:textColor="@color/black"
                                        android:textSize="18sp" />

                                    <ImageView
                                        android:layout_width="100dp"
                                        android:layout_height="100dp"
                                        android:layout_marginTop="10dp"
                                        android:src="@color/black"
                                        android:visibility="gone" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="5dp"
                                android:orientation="horizontal"
                                android:visibility="gone"
                                android:weightSum="3">

                                <ImageView
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:padding="10dp"
                                    android:src="@drawable/phone_pe" />

                                <TextView
                                    android:id="@+id/txt_phonepe"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="PHONNEPE"
                                    android:textColor="@color/black"
                                    android:textIsSelectable="true"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_pp_amt"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="10dp"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="Rs. 10:"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_pp_pay"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="15dp"
                                    android:layout_weight="1"
                                    android:background="@drawable/editbg"
                                    android:fontFamily="sans-serif"
                                    android:text="OPEN"
                                    android:textAlignment="center"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="5dp"
                                android:layout_marginTop="5dp"
                                android:orientation="horizontal"
                                android:visibility="gone"
                                android:weightSum="3">

                                <ImageView
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:padding="10dp"
                                    android:src="@drawable/g_pay" />

                                <TextView
                                    android:id="@+id/txt_gpay"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="G Pay:"
                                    android:textColor="@color/black"
                                    android:textIsSelectable="true"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_gp_amt"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="10dp"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="Rs. 10:"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_gp_pay"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="15dp"
                                    android:layout_weight="1"
                                    android:background="@drawable/editbg"
                                    android:fontFamily="sans-serif"
                                    android:text="OPEN"
                                    android:textAlignment="center"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="5dp"
                                android:layout_marginTop="5dp"
                                android:orientation="horizontal"
                                android:visibility="gone"
                                android:weightSum="3">

                                <ImageView
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:padding="10dp"
                                    android:src="@drawable/pay_tm" />

                                <TextView
                                    android:id="@+id/txt_paytm"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="PayTm:"
                                    android:textColor="@color/black"
                                    android:textIsSelectable="true"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_pt_amt"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="10dp"
                                    android:layout_weight="1"
                                    android:fontFamily="sans-serif"
                                    android:text="Rs. 10:"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/txt_pt_pay"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginEnd="15dp"
                                    android:layout_weight="1"
                                    android:background="@drawable/editbg"
                                    android:fontFamily="sans-serif"
                                    android:text="OPEN"
                                    android:textAlignment="center"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="10dp"
                                android:background="@drawable/d_orange_corner"
                                android:orientation="vertical"
                                android:padding="25dp">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:gravity="center_horizontal"
                                    android:text="Page Expires in "
                                    android:textColor="@color/white"
                                    android:textSize="18sp" />

                                <TextView
                                    android:id="@+id/pay_time"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginTop="5dp"
                                    android:gravity="center_horizontal"
                                    android:text="2:00"
                                    android:textColor="@color/white"
                                    android:textSize="18sp" />

                            </LinearLayout>


                            <LinearLayout
                                android:id="@+id/lnr_utr"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginLeft="10dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginRight="10dp"
                                android:layout_marginBottom="30dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical"
                                android:visibility="gone">
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="20dp"
                                    android:layout_marginTop="20dp"
                                    android:layout_marginEnd="20dp"
                                    android:layout_marginBottom="15dp"
                                    android:fontFamily="sans-serif"
                                    android:text="@string/if_amount_is_not_added_in_your_wallet_please_submit_following_form"
                                    android:textAlignment="center"
                                    android:textColor="@color/black"
                                    android:textSize="18sp" />

                                <EditText
                                    android:id="@+id/edt_utr"
                                    android:layout_width="200dp"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="8dp"
                                    android:background="@drawable/editbg"
                                    android:hint="Enter UTR"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textColorHint="@color/gray_line"
                                    android:visibility="visible" />

                                <EditText
                                    android:id="@+id/edt_amt"
                                    android:layout_width="200dp"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="8dp"
                                    android:background="@drawable/editbg"
                                    android:hint="Enter Amt."
                                    android:inputType="number"
                                    android:maxLines="1"
                                    android:textColor="@color/black"
                                    android:textColorHint="@color/gray_line"
                                    android:visibility="visible" />

                                <Button
                                    android:id="@+id/btn_submitUTR"
                                    android:layout_width="200dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="20dp"
                                    android:background="@drawable/btnbg"
                                    android:gravity="center"
                                    android:text="Submit"
                                    android:textColor="@color/white" />
                            </LinearLayout>


                        </LinearLayout>

                    </androidx.core.widget.NestedScrollView>


                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>


    </androidx.core.widget.NestedScrollView>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:background="@color/rewardBrownz"
        android:gravity="center_horizontal"
        android:padding="5dp"
        android:text="@string/please_do_not_press_back_button_until_payment_is_complete"
        android:textColor="@color/black"
        android:textSize="12sp" />

</RelativeLayout>