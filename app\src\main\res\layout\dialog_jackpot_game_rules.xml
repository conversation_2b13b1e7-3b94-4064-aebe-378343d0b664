<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    style="@style/dialogParentStyle">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@id/lnr_box"
        android:layout_alignRight="@id/lnr_box"
        android:layout_alignTop="@id/lnr_box"
        android:layout_alignBottom="@id/lnr_box" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginBottom="@dimen/dp20">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp20"
                android:paddingBottom="@dimen/dp20">

                <!-- Game Overview -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎯 Game Overview"
                    android:textColor="@color/gold_color"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Jackpot 3 Patti is a card-based betting game where you predict the winning hand combination. Place bets on different hand types and win based on the final result."
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_marginBottom="20dp"
                    android:lineSpacingExtra="2dp" />

                <!-- How to Play -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎮 How to Play"
                    android:textColor="@color/gold_color"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• Choose your bet amount using + and - buttons\n• Select the hand type you think will win\n• Place your bet before the timer runs out\n• Watch the cards being dealt\n• Win if your prediction matches the result!"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_marginBottom="20dp"
                    android:lineSpacingExtra="4dp" />

                <!-- Hand Rankings -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🏆 Hand Rankings (Highest to Lowest)"
                    android:textColor="@color/gold_color"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="10dp" />

                <!-- Hand Rankings List -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <!-- SET -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="SET (Trail)"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:5"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- PURE SEQ -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="PURE SEQ"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:4"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- SEQ -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="SEQ (Sequence)"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:3"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- COLOR -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="COLOR (Flush)"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:2"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- PAIR -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="PAIR"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:1.2"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- HIGH CARD -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:background="@drawable/background_border"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp">
                        
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="HIGH CARD"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1:1"
                            android:textColor="@color/gold_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>

                <!-- Hand Explanations -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📋 Hand Explanations"
                    android:textColor="@color/gold_color"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• SET: Three cards of same rank (A-A-A)\n• PURE SEQ: Three consecutive cards of same suit (5♠-6♠-7♠)\n• SEQ: Three consecutive cards of different suits (5♠-6♥-7♣)\n• COLOR: Three cards of same suit (2♠-7♠-K♠)\n• PAIR: Two cards of same rank (A-A-5)\n• HIGH CARD: No matching pattern, highest card wins"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_marginBottom="20dp"
                    android:lineSpacingExtra="4dp" />

                <!-- Tips -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 Pro Tips"
                    android:textColor="@color/gold_color"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• Higher payouts come with lower probability\n• Manage your bankroll wisely\n• Watch betting patterns and trends\n• Use the bet history to track your performance\n• Start with smaller bets to understand the game"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

    <include layout="@layout/dialog_toolbar" />

</RelativeLayout>
