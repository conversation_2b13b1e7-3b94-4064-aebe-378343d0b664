<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@android:color/white"
    android:gravity="center">

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Wallet Option"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center" />

    <!-- Subtitle -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Choose your preferred payment method"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="6dp"
        android:gravity="center" />

    <!-- USDT Option -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:elevation="2dp"
        android:padding="10dp"
        android:gravity="center_vertical"
        android:id="@+id/layout_usdt">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/usdt"
            android:layout_marginEnd="16dp"
            android:scaleType="centerInside" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="USDT Wallet"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cryptocurrency payments"
                android:textSize="12sp"
                android:textColor="@color/gray" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_usdt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Select"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_blue_dark"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp" />

    </LinearLayout>

<!--    &lt;!&ndash; RBM Option &ndash;&gt;-->
<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="horizontal"-->
<!--        android:background="@android:color/white"-->
<!--        android:padding="10dp"-->
<!--        android:layout_marginBottom="5dp"-->
<!--        android:gravity="center_vertical"-->
<!--        android:id="@+id/layout_rbm">-->

<!--        <ImageView-->
<!--            android:layout_width="40dp"-->
<!--            android:layout_height="40dp"-->
<!--            android:src="@drawable/rbm"-->
<!--            android:layout_marginEnd="16dp"-->
<!--            android:scaleType="centerInside" />-->

<!--        <LinearLayout-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1"-->
<!--            android:orientation="vertical">-->

<!--            <TextView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="RBM Wallet"-->
<!--                android:textSize="16sp"-->
<!--                android:textStyle="bold"-->
<!--                android:textColor="@color/black" />-->

<!--            <TextView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="Traditional payment method"-->
<!--                android:textSize="12sp"-->
<!--                android:textColor="@color/gray" />-->

<!--        </LinearLayout>-->

<!--        <Button-->
<!--            android:id="@+id/btn_rbm"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="Select"-->
<!--            android:textColor="@android:color/white"-->
<!--            android:background="@android:color/holo_blue_dark"-->
<!--            android:paddingHorizontal="20dp"-->
<!--            android:paddingVertical="8dp" />-->

<!--    </LinearLayout>-->

    <!-- Note -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="⚠️ IMPORTANT: This is a one-time selection and cannot be changed later. Please choose carefully."
        android:textSize="10sp"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:background="#FFEBEE"
        android:elevation="2dp"
        android:padding="12dp"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
