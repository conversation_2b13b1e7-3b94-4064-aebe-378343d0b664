<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".Activity.PaymentScreen.Payment_Upload_A">

    <androidx.core.widget.NestedScrollView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/toolbr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_bg"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/colorPrimary"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/bt_menu"
                        android:layout_width="?attr/actionBarSize"
                        android:layout_height="?attr/actionBarSize"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="20dp"
                        android:tint="@android:color/white"
                        app:srcCompat="@drawable/ic_back_arrow" />

                    <TextView
                        android:id="@+id/toolbr_lbl"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:text="@string/app_name"
                        android:textAlignment="textStart"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/upload"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:text="Upload"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                tools:context=".Activity.AllPaymentActivity">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/lyTop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:background="@color/colorPrimary"
                        android:minHeight="50dp"
                        android:orientation="vertical"
                        android:paddingStart="15dp"
                        android:paddingTop="15dp"
                        android:paddingEnd="15dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txn_id"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:text="Transaction Id: "
                                android:textColor="@color/white"
                                android:textSize="16sp"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/copy_1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="10dp"
                                android:background="@drawable/d_background_border_white"
                                android:padding="3dp"
                                android:text=" copy "
                                android:textAlignment="center"
                                android:textColor="@color/white"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_diaPhone"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginStart="3dp"
                                android:gravity="center"
                                android:text="Mobile No.:"
                                android:textColor="@color/white"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/copy_2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="10dp"
                                android:background="@drawable/d_background_border_white"
                                android:padding="3dp"
                                android:text=" copy "
                                android:textAlignment="center"
                                android:textColor="@color/white"
                                android:textSize="16sp" />

                        </LinearLayout>


                        <TextView
                            android:id="@+id/txt_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginStart="3dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:gravity="center"
                            android:text="Amount:"
                            android:textColor="@color/white"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <!--Main Content-->
                    <androidx.core.widget.NestedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@+id/lyTop"
                        android:fillViewport="true">

                        <!--Details-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="@dimen/spacing_smlarge"
                                    android:layout_marginLeft="@dimen/spacing_smlarge"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:id="@+id/view_1"
                                        android:layout_width="2dp"
                                        android:layout_height="match_parent"
                                        android:layout_centerHorizontal="true"
                                        android:background="@color/gray_line"
                                        android:orientation="vertical" />

                                    <ImageView
                                        android:id="@+id/tick_1"
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        app:srcCompat="@drawable/gray_tick" />

                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/card_1"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/spacing_middle"
                                    android:layout_marginRight="@dimen/spacing_middle"
                                    android:layout_marginBottom="@dimen/spacing_medium"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    app:cardCornerRadius="2dp"
                                    app:cardElevation="1dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@string/if_you_have_paid_please_upload_a_screenshot"
                                        android:textColor="@color/gray"
                                        android:textSize="18sp" />

                                    <LinearLayout
                                        android:id="@+id/upload_img"
                                        android:layout_width="100dp"
                                        android:layout_height="100dp"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginTop="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:id="@+id/image_"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:visibility="visible" />

                                    </LinearLayout>

                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="@dimen/spacing_smlarge"
                                    android:layout_marginLeft="@dimen/spacing_smlarge"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:id="@+id/view_2"
                                        android:layout_width="2dp"
                                        android:layout_height="match_parent"
                                        android:layout_centerHorizontal="true"
                                        android:background="@color/gray_line" />

                                    <ImageView
                                        android:id="@+id/tick_2"
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        app:srcCompat="@drawable/gray_tick" />

                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/card_2"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/spacing_middle"
                                    android:layout_marginRight="@dimen/spacing_middle"
                                    android:layout_marginBottom="@dimen/spacing_medium"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    app:cardCornerRadius="2dp"
                                    app:cardElevation="1dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="the recharge agents processing"
                                        android:textColor="@color/gray"
                                        android:textSize="18sp" />

                                    <ImageView
                                        android:layout_width="100dp"
                                        android:layout_height="100dp"
                                        android:layout_marginTop="10dp"
                                        android:src="@color/gray"
                                        android:visibility="invisible" />

                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="@dimen/spacing_smlarge"
                                    android:layout_marginLeft="@dimen/spacing_smlarge"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:id="@+id/view_3"
                                        android:layout_width="2dp"
                                        android:layout_height="match_parent"
                                        android:layout_centerHorizontal="true"
                                        android:background="@color/gray_line" />

                                    <ImageView
                                        android:id="@+id/tick_3"
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        android:layout_marginTop="10dp"
                                        app:srcCompat="@drawable/gray_tick" />

                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/card_3"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="@dimen/spacing_middle"
                                    android:layout_marginRight="@dimen/spacing_middle"
                                    android:orientation="vertical"
                                    android:visibility="visible"
                                    app:cardCornerRadius="2dp"
                                    app:cardElevation="1dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@string/completed"
                                        android:textColor="@color/gray"
                                        android:textSize="18sp" />


                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>

                    </androidx.core.widget.NestedScrollView>


                </RelativeLayout>

            </LinearLayout>
        </LinearLayout>


    </androidx.core.widget.NestedScrollView>

</LinearLayout>