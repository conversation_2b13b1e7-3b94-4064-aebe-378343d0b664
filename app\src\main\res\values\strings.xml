<resources>
    <string name="highScore">Highscore:\u0020</string>
    <string name="score">Score:\u0020</string>
    <string name="heads">Heads</string>
    <string name="tails">Tails</string>
    <string name="new_highscore">New Highscore !!!</string>
    <string name="heads_first_letter">H</string>
    <string name="tails_first_letter">T</string>
    <string name="app_name">Gaming27</string>
    <!--    <string name="deep_link_url">www.deeplinkurl.com</string>-->
    <string name="deep_link_url">games.androappstech.in</string>
    <string name="razor_pay_test">rzp_test_SJyQBaZflutttlZOjC9</string>
    <string name="facebook_app_id">319765683034434</string>
    <string name="fb_login_protocol_scheme">fb319765683034434</string>
    <string name="chaal_error_messsage">Wait for you chaal</string>
    <string name="sort_error_message">Sort First!</string>
    <string name="select_card_error_message">Please select atleast one Card</string>
    <string name="declare_game">Your opponent declare the game</string>
    <string name="minimum_grouping">Minimum 3 cards Needed For Grouping </string>
    <string name="minimum_grouping2">Minimum 2 cards Needed For Grouping </string>
    <string name="drop">Drop</string>
    <string name="declare">Declare</string>
    <string name="declare_back">Declare Back</string>
    <string name="group">GROUP</string>
    <string name="discard">Discard</string>
    <string name="finish">Finish</string>
    <string name="drop_message">Continuous \"Please Wait\" after initial round of Win.</string>
    <string name="cards_getting_error">\"Please Wait\" we are getting some error while finding cards for you.</string>
    <string name="please_wait_game_is_going_on">\"Please Wait\" game is going on.</string>
    <string name="tow_palyer">2\nPlayers</string>
    <string name="five_player">5\nPlayers</string>
    <string name="select_no_of_players">Select No. of Players</string>
    <string name="play_now">Play Now</string>
    <string name="terms_and_conditions">Terms and Conditions</string>
    <string name="please_check_internet_connection">Please Check Internet Connection</string>
    <!--Payment Activity START-->
    <string name="payment_methods">Payment Methods</string>
    <string name="account_copied_successfully">Account Copied Successfully</string>
    <string name="imp_note">*You Must click to get th latest account every time you recharge, otherwise the account cannot be received quickly.</string>
    <string name="imp_note_2">*Recharge need to be completed within 10 minute. </string>
    <string name="upload">Upload</string>
    <string name="if_you_have_paid_please_upload_a_screenshot">If you have paid , please upload a screenshot</string>
    <string name="completed">Complete</string>
    <string name="teenpatti">Teenpatti</string>
    <string name="andharbahar">AndharBahar</string>
    <string name="aviator">Aviator</string>
    <string name="online_ludo">Online Ludo</string>
    <string name="dragontiger">DragonTiger</string>
    <string name="share_message">Download the APP and Enjoy With Your Friends</string>
    <string name="table_points">Table Points</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="Helvetica_Bold">fonts/Helvetica-Bold.ttf</string>
    <string name="Helvetica_Bold_Extra">fonts/helvetica-rounded-bold-5871d05ead8de.otf</string>
    <string name="login">Login</string>
    <string name="send_request">Send request</string>
    <string name="wrong_declare">Wrong Declare</string>
    <string name="teen_patti">Teen Patti</string>
    <string name="rummy_2_player">Rummy 2 Player</string>
    <string name="rummy_5_player">Rummy 5 Player</string>
    <string name="poker_game">Poker Game</string>
    <string name="rounds">Rounds</string>
    <string name="players">Players</string>
    <string name="name">\"Name: \"</string>
    <string name="upi">UPI:</string>
    <string name="result_game_id">\"Result | Game ID : \"</string>
    <string name="get_ready_next_game_start_in">\"Get Ready - Next game start in \"</string>
    <string name="seconds">second(s)</string>
    <string name="game_start">Game start</string>
    <string name="winner">Winner</string>
    <string name="player_2">Player 2</string>
    <string name="player_3">Player 3</string>
    <string name="player_4">Player 4</string>
    <string name="player_5">Player 5</string>
    <string name="player_6">Player 6</string>
    <string name="player_7">Player 7</string>
    <string name="please_wait_for_other_players">Please Wait For Other Players!</string>
    <string name="share_wallet">Share Wallet</string>
    <string name="player1">Player1</string>
    <string name="username">Username</string>
    <string name="total_points">Total Points :</string>
    <string name="rummy">Rummy</string>
    <string name="submit">Submit</string>
    <string name="someone_has_asking_for_share_wallet">Someone has asking for share wallet</string>
    <string name="_2_s">2 s</string>
    <string name="you">You</string>
    <string name="points">points</string>
    <string name="min_pot">Min Pot</string>
    <string name="viewer_status">Viewer Status</string>
    <string name="how_to_play">HOW TO PLAY</string>
    <string name="serial_no">Serial no</string>
    <string name="status">Status</string>
    <string name="game_points_game_id">Game Points | Game ID :</string>
    <string name="create_group">Create Table</string>
    <string name="id">id</string>
    <string name="tablename">TableName</string>
    <string name="max_player">Max.Player</string>
    <string name="online">Online</string>
    <string name="action">Action</string>
    <string name="i_have_read_and_agree_with_the_above">i have read and agree with the above terms and conditions</string>
    <string name="click_here_to_accept">Click here to accept</string>
    <string name="view_game">View</string>
    <string name="please_pick_and_drop_card">Please pick and drop card</string>
    <string name="sort">sort</string>
    <string name="get_ready_to_end">Get ready to End</string>
    <string name="CURRENCY_SYMBOL">₹</string>
    <string name="change_card">Change Card</string>
    <string name="permission_denied_contacts">Permission Denied</string>

    <string name="sevenupdown">7upDown</string>
    <string name="jackpotteenpatti">Jackpot3patti</string>
    <string name="animal_roulette">Animal roulette</string>
    <string name="car_roulette">Car roulette</string>
    <string name="color_prediction">Color prediction</string>
    <string name="color_prediction_1min">Color prediction 1 Min</string>
    <string name="rummy_pool">Rummy pool</string>
    <string name="rummy_deal">Rummy deal</string>
    <string name="rummy_private">Rummy Private</string>
    <string name="head_tails">Head &amp; Tails</string>
    <string name="baccarte">Baccarate</string>
    <string name="roulette">Roulette</string>
    <string name="roulettedouble">Roulette Double</string>
    <string name="jhandi_munda">Jhandi Munda</string>
    <string name="poker">Poker</string>
    <string name="red_black">Red vs Black</string>
    <string name="close">Close</string>

</resources>
