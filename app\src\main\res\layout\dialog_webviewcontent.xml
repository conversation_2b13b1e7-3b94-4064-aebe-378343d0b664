<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    style="@style/dialogParentStyle"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        >

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            style="@style/popUpBoxbg"
            android:orientation="vertical"
            android:id="@+id/lnr_box"
            android:layout_marginTop="-40dp"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_marginHorizontal="25dp"
            android:padding="20dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_marginBottom="30dp"
                >


                <WebView
                    android:id="@+id/webview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="15dp"
                    android:layout_marginLeft="5dp"
                    />


            </LinearLayout>

            <TextView
                android:id="@+id/txtnotfound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="Data no available!"
                android:gravity="center"
                android:visibility="gone"
                />

            <RelativeLayout
                android:id="@+id/rlt_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="300dp"
                    android:layout_height="100dp"
                    style="?android:attr/progressBarStyle"
                    android:indeterminateDrawable="@drawable/cpb_3"
                    android:indeterminateDuration="4000"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_gravity="center"
                    android:layout_centerInParent="true"
                    android:visibility="visible"
                    />

            </RelativeLayout>





        </RelativeLayout>

        <include
            layout="@layout/dialog_toolbar"/>

    </RelativeLayout>



</RelativeLayout>