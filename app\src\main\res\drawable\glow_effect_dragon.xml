<?xml version="1.1" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item android:right="5dp" android:top="0dp">
                <shape>
                    <corners android:radius="5dp" />
                    <gradient
                        android:centerX="70%"
                        android:startColor="#804CD6FF"
                        android:centerColor="#8004485C"
                        android:endColor="#804CD6FF"
                        android:type="linear"
                        />
                </shape>
            </item>
            <item android:bottom="5dp" android:left="6dp" android:right="10dp" android:top="5dp">
                <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle" >
                    <corners
                        android:radius="5dp"
                        />
                    <gradient
                        android:angle="90"
                        android:centerX="30%"
                        android:centerColor="@color/transparent"
                        android:startColor="#8008c3fa"
                        android:endColor="@color/transparent"
                        android:type="linear"
                        />
                    <size
                        android:width="270dp"
                        android:height="60dp"
                        />
                    <stroke
                        android:width="2.5dp"
                        android:color="#08c3fa"
                        />
                </shape>
            </item>
            <item android:bottom="5dp" android:left="6dp" android:right="10dp" android:top="5dp">
                <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle" >
                    <corners
                        android:radius="5dp"
                        />

                    <gradient
                        android:angle="90"
                        android:centerX="70%"
                        android:centerColor="@color/transparent"
                        android:startColor="@color/transparent"
                        android:endColor="#8008c3fa"
                        android:type="linear"
                        />

                    <size
                        android:width="270dp"
                        android:height="60dp"
                        />
                </shape>
            </item>
            <!-- the glow for the half circles, possible with radius as well. I liked
                 android:type="sweep" better because radius tends to fade out.-->
            <item android:bottom="5dp" android:left="6dp" android:right="10dp" android:top="5dp">
                <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle" >
                    <corners
                        android:radius="5dp"
                        />

                    <gradient
                        android:angle="360"
                        android:centerX="5%"
                        android:centerColor="#8008c3fa"
                        android:startColor="@color/transparent"
                        android:endColor="@color/transparent"
                        android:gradientRadius="360"
                        android:type="sweep"
                        />

                    <size
                        android:width="270dp"
                        android:height="60dp"
                        />
                </shape>
            </item>
            <item android:bottom="5dp" android:left="6dp" android:right="10dp" android:top="5dp">
                <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle" >
                    <corners
                        android:radius="5dp"
                        />

                    <gradient
                        android:angle="360"
                        android:centerX="95%"
                        android:centerColor="@color/transparent"
                        android:startColor="#8008c3fa"
                        android:endColor="#8008c3fa"
                        android:gradientRadius="360"
                        android:type="sweep"
                        />

                    <size
                        android:width="270dp"
                        android:height="60dp"
                        />
                </shape>
            </item>
        </layer-list>
    </item>

</selector>