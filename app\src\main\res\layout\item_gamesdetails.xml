<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:gravity="center"
    android:animateLayoutChanges="true"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:layout_marginBottom="@dimen/dimen_10dp"
        >

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/ic_game_console"
            android:layout_marginBottom="5dp"
            />

        <TextView
            android:id="@+id/tvHeading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Games"
            style="@style/ShadowWhiteTextview"
            android:textAllCaps="true"
            android:fontFamily="cursive"
            android:textColor="@color/gold"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>


</LinearLayout>