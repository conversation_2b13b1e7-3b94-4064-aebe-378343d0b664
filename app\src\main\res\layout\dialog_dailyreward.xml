<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/dialogParentStyle"
    android:gravity="center">

    <RelativeLayout
        android:id="@+id/rlt_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            style="@style/GameHeadingTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:text="Daily bonus" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="25dp"
            android:layout_marginTop="@dimen/pop_up_top_margin"
            style="@style/popUpBoxbg"
            android:padding="20dp"
            android:layout_weight="1"
            >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/reward_rec"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                tools:itemCount="1"
                tools:listitem="@layout/reward_itemview" />

            <TextView
                android:id="@+id/txtnotfound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="Data no available!"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/rlt_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="300dp"
                    android:layout_height="100dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:indeterminateDrawable="@drawable/cpb_3"
                    android:indeterminateDuration="4000"
                    android:visibility="visible" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <Button
                android:id="@+id/btnCollect"
                style="@style/ShadowWhiteTextview"
                android:layout_width="200dp"
                android:layout_height="55dp"
                android:background="@drawable/ic_button"
                android:elevation="2dp"
                android:gravity="center"
                android:text="Collect"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="18sp" />
        </RelativeLayout>

    </LinearLayout>
    <include
        layout="@layout/dialog_toolbar"/>
</RelativeLayout>