<?xml version="1.1" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_centerHorizontal="true"
    android:layout_centerInParent="true"
    android:paddingBottom="@dimen/dp40"
    android:background="@color/blue"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="center_horizontal"
        android:gravity="center_horizontal"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:textAlignment="center"
            android:text="Enter Private Code"
            android:layout_marginTop="@dimen/dp40"
            android:layout_marginBottom="@dimen/dp10"
            android:textColor="@color/white"
            android:textSize="@dimen/payu_dimen_20sp" />

        <EditText
            android:layout_below="@+id/login"
            android:id="@+id/et_code"
            android:layout_width="@dimen/login_edit_width"
            android:layout_height="@dimen/login_edit_height"
            android:layout_centerHorizontal="true"
            android:background="@drawable/bg_edit_box"
            android:fontFamily="@font/poppins_regular"
            android:hint="Enter Code"
            android:layout_marginTop="@dimen/dp20"
            android:inputType="number"
            android:paddingLeft="@dimen/dp15"
            android:textColor="@color/white"
            android:textColorHint="@color/white"
            android:textSize="@dimen/payu_dimen_14sp" />
        <Button
            android:id="@+id/btn_join_private"
            android:layout_below="@+id/et_code"
            android:layout_width="@dimen/payu_dimen_200dp"
            android:layout_height="@dimen/payu_dimen_45dp"
            android:gravity="center"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="@dimen/dp15"
            android:textColor="@color/black"
            android:visibility="visible"
            android:text="Join"
            android:background="@drawable/btn_yellow_discard"
            android:backgroundTint="@color/yellow1"
            />


    </RelativeLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>