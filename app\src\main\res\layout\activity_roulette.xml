<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/btn_green_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Button
        android:id="@+id/spinBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="false"
        android:layout_centerHorizontal="true"
        android:text="SPIN"
        android:layout_marginBottom="15dp"
        />

    <TextView
        android:id="@+id/resultTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginTop="@dimen/dp50"
        android:textAllCaps="true"
        android:textSize="@dimen/payu_dimen_20sp"
        android:layout_centerHorizontal="true"
        android:text="0 Black"/>

    <ImageView
        android:id="@+id/wheel"
        android:layout_width="190dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="false"
        android:layout_centerVertical="true"
        android:adjustViewBounds="true"
        android:scaleType="centerInside"
        android:rotation="1"
        app:srcCompat="@drawable/wheel_36old" />

    <ImageView
        android:id="@+id/triangle"
        android:layout_width="@dimen/dp20"
        android:layout_height="@dimen/dp20"
        android:layout_centerVertical="true"
        android:layout_above="@id/wheel"
        android:layout_marginLeft="85dp"
        android:tint="@color/orange"
        app:srcCompat="@drawable/triangle"
        />

    <LinearLayout
        android:layout_below="@+id/resultTv"
        android:id="@+id/lnr_firstRow"
        android:layout_marginLeft="@dimen/payu_dimen_200dp"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
    </LinearLayout>
    <LinearLayout
        android:layout_below="@+id/lnr_firstRow"
        android:id="@+id/lnr_secondRow"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginLeft="@dimen/payu_dimen_200dp"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
    </LinearLayout>

    <LinearLayout
        android:layout_centerHorizontal="true"
        android:id="@+id/lnrfollowColr"
        android:gravity="center_horizontal"
        android:layout_width="wrap_content"
        android:layout_marginLeft="@dimen/dp50"
        android:layout_alignParentBottom="true"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
    </LinearLayout>
    <include
        android:id="@+id/lnrtypegame"
        layout="@layout/view_user_bottom_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

</RelativeLayout>