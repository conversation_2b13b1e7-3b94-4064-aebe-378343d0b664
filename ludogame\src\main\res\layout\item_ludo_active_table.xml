<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/table_list_color"
            android:elevation="2dp"
            android:paddingTop="13dp"
            android:paddingBottom="13dp">

            <LinearLayout
                android:id="@+id/lnrValueBoot"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tvBoot"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:text="0.3"
                    android:textColor="@color/white"
                    android:textSize="@dimen/table_text_size" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/lnrValuePlayer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/tvTotalPlayer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:text="3955"
                    android:textColor="@color/white"
                    android:textSize="@dimen/table_text_size" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrValueJoin"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1.3"
                android:gravity="center">

                <TextView
                    android:id="@+id/tvPlaynow"
                    android:layout_width="75dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center"
                    android:background="@drawable/button_background_green"
                    android:gravity="center"
                    android:text="Play now"
                    android:includeFontPadding="false"
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>