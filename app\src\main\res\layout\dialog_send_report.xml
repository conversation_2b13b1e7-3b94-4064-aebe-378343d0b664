<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    >

    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:padding="5dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/d_toolbar_bg"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:layout_marginBottom="20dp"
                >

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="TITTLE"
                    android:textAllCaps="true"
                    android:textSize="18sp"
                    android:layout_weight="1"
                    app:fontFilePath="@string/Helvetica_Bold_Extra"
                    />

                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_close_new"
                    />

            </LinearLayout>

            <EditText
                android:id="@+id/edtReportDecriction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="100dp"
                android:layout_marginBottom="20dp"
                android:hint="Enter Report"
                android:textColorHint="@color/gray"
                android:gravity="top"
                android:padding="10dp"
                android:background="@drawable/d_white_gray_borader_round"
                android:inputType="textMultiLine"
                android:singleLine="false"
                android:elevation="5dp"
                android:textColor="@color/black"
                />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="10dp"
                >

                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_yes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardBackgroundColor="#397E23"
                    app:cardCornerRadius="5dp"
                    android:layout_marginRight="10dp"
                    >

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingRight="20dp"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="YES"
                            android:textSize="18sp"
                            android:layout_weight="1"
                            app:fontFilePath="@string/Helvetica_Bold_Extra"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:textAllCaps="true"
                            />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/bt_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardBackgroundColor="#337DD6"
                    app:cardCornerRadius="5dp"
                    android:layout_marginLeft="10dp"
                    >

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingRight="20dp"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="NO"
                            android:textSize="18sp"
                            android:layout_weight="1"
                            app:fontFilePath="@string/Helvetica_Bold_Extra"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:textAllCaps="true"
                            />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>


            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>