<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginBottom="@dimen/dp5"
    >

    <androidx.cardview.widget.CardView
        android:layout_weight="0.5"
        app:cardBackgroundColor="#F56D3D"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Sr No."
            />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_weight=".8"
        app:cardBackgroundColor="#F56D3D"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Amount"
            />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_weight=".5"
        app:cardBackgroundColor="#F56D3D"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Mode"
            />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_weight="1.0"
        app:cardBackgroundColor="#F56D3D"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Status"
            />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_weight="1.0"
        app:cardBackgroundColor="#F56D3D"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Tx Hash"
            />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_weight="1.5"
        app:cardBackgroundColor="#F21E48"
        style="@style/colorLastWinHeadingbg"
        >

        <TextView
            style="@style/colorLastWinHeadingText"
            android:text="Date"
            />

    </androidx.cardview.widget.CardView>

</LinearLayout>