<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    android:gravity="center"
    android:paddingLeft="0dp"
    android:paddingTop="0dp"
    android:paddingRight="0dp"
    android:paddingBottom="0dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="._baccarat.Baccarat_A">

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/back"
        android:visibility="visible" />

    <ImageView
        android:layout_below="@+id/imgback"
        android:layout_width="@dimen/dimen_40dp"
        android:layout_height="@dimen/dimen_40dp"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/dp10"
        android:layout_marginTop="@dimen/dimen_10dp"
        android:onClick="openGameRules"
        android:src="@drawable/ic_jackpot_info"
        android:visibility="visible" />

    <include
        android:id="@+id/lnrtypegame"
        layout="@layout/view_user_bottom_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

    <RelativeLayout
        android:layout_width="550dp"
        android:layout_height="match_parent"
        android:layout_centerInParent="true">

        <RelativeLayout
            android:id="@+id/lnrCardsView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignLeft="@id/lnrtopviewcontainer"
                android:layout_alignTop="@id/lnrtopviewcontainer"
                android:layout_alignRight="@id/lnrtopviewcontainer"
                android:layout_alignBottom="@id/lnrtopviewcontainer"
                android:background="@drawable/ic_jackpot_topview" />

            <LinearLayout
                android:id="@+id/lnrtopviewcontainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="5dp"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dimen_15dp"
                android:paddingTop="5dp"
                android:paddingRight="@dimen/dimen_15dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginRight="@dimen/dp10"
                        android:src="@drawable/p_player"
                        android:visibility="visible" />

                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dimen_10dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="@dimen/jack_pot_box_width"
                            android:layout_height="@dimen/jack_pot_box_height"
                            android:gravity="center_horizontal"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/lnrRedCardsParent"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_marginRight="@dimen/dp20"
                                android:gravity="center_vertical|right"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginRight="-20dp"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal">

                                    <ImageView
                                        android:id="@+id/ivJackpotCard1"
                                        android:layout_width="80dp"
                                        android:layout_height="80dp"
                                        android:src="@drawable/card_bg"
                                        android:visibility="visible" />
                                </RelativeLayout>

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginRight="-20dp"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal">

                                    <ImageView
                                        android:id="@+id/ivJackpotCard2"
                                        android:layout_width="80dp"
                                        android:layout_height="80dp"
                                        android:layout_marginLeft="3dp"
                                        android:layout_marginRight="3dp"
                                        android:src="@drawable/card_bg" />
                                </RelativeLayout>

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/rlt_centercard"
                                    android:layout_weight="1"

                                    >

                                    <ImageView
                                        android:id="@+id/ivJackpotCard3"
                                        android:layout_width="80dp"
                                        android:layout_height="80dp"
                                        android:layout_centerHorizontal="true"
                                        android:src="@drawable/card_bg" />
                                </RelativeLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </RelativeLayout>

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:src="@drawable/ic_verse" />

                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp20"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/lnrBlackCardsParent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:gravity="center_vertical|right"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_horizontal">

                                <ImageView
                                    android:layout_width="80dp"
                                    android:layout_height="80dp"
                                    android:src="@drawable/card_bg"
                                    android:visibility="visible" />
                            </RelativeLayout>

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_horizontal">

                                <ImageView
                                    android:layout_width="80dp"
                                    android:layout_height="80dp"
                                    android:layout_marginLeft="3dp"
                                    android:layout_marginRight="3dp"
                                    android:src="@drawable/card_bg" />
                            </RelativeLayout>

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_toRightOf="@+id/rlt_centercard"
                                android:layout_weight="1">

                                <ImageView
                                    android:layout_width="80dp"
                                    android:layout_height="80dp"
                                    android:layout_centerHorizontal="true"
                                    android:src="@drawable/card_bg" />
                            </RelativeLayout>
                        </LinearLayout>
                    </RelativeLayout>

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginRight="@dimen/dp10"
                        android:src="@drawable/b_banker"
                        android:visibility="visible" />
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-25dp"
                    android:layout_marginBottom="5dp">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignLeft="@id/horizontal"
                        android:layout_alignTop="@id/horizontal"
                        android:layout_alignRight="@id/ivMorewins"
                        android:layout_alignBottom="@id/horizontal"
                        android:background="@drawable/ic_jackpot_change_strip" />

                    <HorizontalScrollView
                        android:id="@+id/horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@+id/ivMorewins"
                        android:fillViewport="true"
                        android:paddingLeft="5dp"
                        android:paddingTop="3dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="3dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/lnrcancelist"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <include layout="@layout/item_lastbet" />
                        </LinearLayout>
                    </HorizontalScrollView>

                    <!--                    <ImageView-->
                    <!--                        android:id="@+id/ivMorewins"-->
                    <!--                        android:layout_width="50dp"-->
                    <!--                        android:layout_height="30dp"-->
                    <!--                        android:layout_alignParentRight="true"-->
                    <!--                        android:layout_centerVertical="true"-->
                    <!--                        android:background="@drawable/btn_oragen_drop"-->
                    <!--                        android:onClick="openJackpotLasrWinHistory"-->
                    <!--                        android:padding="@dimen/dp7"-->
                    <!--                        android:src="@drawable/ic_arrow_zigzag" />-->
                </RelativeLayout>
            </LinearLayout>

<!--            <TextView-->
<!--                android:id="@+id/tvStartTimer"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_centerHorizontal="true"-->
<!--                android:layout_marginTop="5dp"-->
<!--                android:layout_marginBottom="5dp"-->
<!--                android:shadowColor="@color/black"-->
<!--                android:shadowDx="1"-->
<!--                android:shadowDy="1"-->
<!--                android:shadowRadius="3"-->
<!--                android:text="30s"-->
<!--                android:textColor="@color/white"-->
<!--                android:textSize="@dimen/jackpot_heading_size" />-->

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:paddingTop="20dp"
                android:visibility="visible">

<!--                <TextView-->
<!--                    android:id="@+id/txtGameRunning"-->
<!--                    style="@style/ShadowGoldTextviewNew"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_centerHorizontal="true"-->
<!--                    android:gravity="center"-->
<!--                    android:text="Please wait for Next Round"-->
<!--                    android:textSize="17sp"-->
<!--                    android:textStyle="bold"-->
<!--                    android:visibility="gone" />-->

                <TextView
                    android:id="@+id/txtcountdown"
                    style="@style/ShadowWhiteTextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text=""
                    android:textColor="#EEC283"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/txtGameBets"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:src="@drawable/place_your_bet"
                    android:visibility="gone" />
            </RelativeLayout>
        </RelativeLayout>
        <!--// Also change from table image if changing-->
        <ImageView
            android:id="@+id/imgTable"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/rtlGameContainer"
            android:layout_alignTop="@id/rtlGameContainer"
            android:layout_alignRight="@id/rtlGameContainer"
            android:layout_alignBottom="@id/rtlGameContainer"
            android:layout_centerHorizontal="true"
            android:background="@drawable/ic_jackpot_box"
            android:visibility="visible" />

        <RelativeLayout
            android:id="@+id/rtlGameContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/lnrCardsView"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="-5dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/lnrtopviewcontainer"
                    android:layout_marginTop="5dp"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/dimen_15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="@dimen/dimen_15dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rec_rules"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_baccarat_setamount" />


                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ChipstoUser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignLeft="@id/txtBallence"
                android:layout_alignTop="@id/txtBallence"
                android:layout_alignRight="@id/txtBallence"
                android:layout_alignBottom="@id/txtBallence"
                android:scaleType="fitXY"
                android:src="@drawable/ic_dt_user_conis" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:layout_marginLeft="22dp"
                android:layout_marginRight="2dp"
                android:gravity="center_vertical"
                android:minWidth="150dp"
                android:paddingLeft="45dp"
                android:text="0"
                android:textColor="@color/gold_color"
                android:textSize="14dp"
                android:textStyle="bold" />
        </RelativeLayout>
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/sticker_animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@drawable/winner_patti"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text="You Win"
                android:textColor="@color/white"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivWine"
                app:layout_constraintEnd_toEndOf="@+id/ivWine"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivWine" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivlose"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/ic_game_over"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
               />

            <TextView
                android:id="@+id/tvlose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/BrownColor"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivlose"
                app:layout_constraintEnd_toEndOf="@+id/ivlose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivlose" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltOngoinGame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:id="@+id/txtGameRunning"
            android:layout_width="@dimen/dp300"
            android:layout_height="@dimen/dp50"
            android:src="@drawable/waiting_for_next"
            android:visibility="visible" />


    </RelativeLayout>

    <RelativeLayout
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp80"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginRight="@dimen/dp10" >

        <ImageView
            android:layout_width="@dimen/dp80"
            android:layout_height="@dimen/dp80"
            android:layout_centerHorizontal="true"
            android:scaleType="centerCrop"
            android:src="@drawable/watch"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tvStartTimer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/payu_dimen_27dp"
            android:shadowColor="#5d5534"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text="0"
            android:textColor="#5d5534"
            android:textSize="@dimen/dp20"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>