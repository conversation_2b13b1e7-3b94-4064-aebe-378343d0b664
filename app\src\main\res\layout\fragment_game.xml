<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/dimen_50dp"
        >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_gameslist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="4dp"
            tools:itemCount="2"
            tools:listitem="@layout/item_gamelist"
            android:layout_gravity="center"
            />


    </LinearLayout>

    <TextView
        android:id="@+id/txtNodata"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="No Data Available"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        android:layout_marginLeft="5dp"
        />

</RelativeLayout>