<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center">

    <RelativeLayout
        android:layout_width="550dp"
        android:layout_height="330dp"
        android:background="@drawable/notic_bg_new">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:scrollbars="vertical"
                    android:paddingTop="40dp"
                    android:visibility="gone"
                    android:text="@string/app_name"
                    android:textAllCaps="true"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    app:fontFilePath="@string/Helvetica_Bold_Extra" />

            <ImageView
                android:id="@+id/imgcontent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp70"
                android:layout_marginBottom="@dimen/dp20"
                android:layout_marginRight="20dp"
                android:layout_marginLeft="20dp"
                android:src="@drawable/home_bg2"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/imgclosetop"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@drawable/ic_close_new"
                android:visibility="visible" />

        </RelativeLayout>

    </RelativeLayout>

</LinearLayout>