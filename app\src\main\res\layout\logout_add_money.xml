<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:layout_marginLeft="@dimen/dp5"
    android:layout_marginRight="@dimen/dp5"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        >

        <LinearLayout
            android:id="@+id/lnr_join"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:background="#6201E6"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:paddingTop="@dimen/dimen_12dp"
            android:paddingBottom="@dimen/dp10">

            <TextView
                android:id="@+id/tv_heading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Join Green"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="18sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra" />

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_close_new"
                android:visibility="invisible" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp10"
            android:layout_below="@+id/lnr_join"
            android:layout_marginRight="@dimen/dp10"
            android:orientation="vertical"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Contract Money"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp10"
            >

            <Button
                android:id="@+id/btn_1"
                android:text="₹  1"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />
            <Button
                android:id="@+id/btn_10"
                android:text="₹  10"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />
            <Button
                android:id="@+id/btn_100"
                android:text="₹  50"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />

        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp20"
            >
            <Button
                android:id="@+id/btn_1000"
                android:text="₹  100"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />
            <Button
                android:id="@+id/btn_10000"
                android:text="₹  1000"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />
            <Button
                android:id="@+id/btn_5000"
                android:text="₹  5000"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/lnrfollow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="visible"
            android:layout_marginLeft="@dimen/dp20"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp10"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            >

        </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Number"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginTop="@dimen/dp10"
                android:textStyle="bold"
                app:fontFilePath="@string/Helvetica_Bold_Extra" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginBottom="@dimen/dp20"
            >

            <Button
                android:id="@+id/btn_minus"
                android:text="-"
                android:layout_marginRight="@dimen/dp15"
                android:textStyle="bold"
                android:textSize="@dimen/dimen_18sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <EditText
                android:id="@+id/edt_total"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp10"
                android:hint="0"
                android:text="0"
                android:clickable="false"
                android:focusable="false"
                android:maxLength="2"
                android:textAlignment="center"
                android:textColorHint="@color/gray"
                android:padding="5dp"
                android:background="@drawable/d_white_gray_borader_round"
                android:inputType="number"
                android:singleLine="false"
                android:elevation="5dp"
                android:textColor="@color/black"
                />

            <Button
                android:id="@+id/btn_plus"
                android:text="+"
                android:textStyle="bold"
                android:textSize="@dimen/dimen_18sp"
                android:layout_width="wrap_content"
                android:layout_marginLeft="@dimen/dp15"
                android:layout_height="wrap_content"/>

        </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fees: 10%"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:visibility="gone"
                android:textSize="16sp"
                android:textStyle="normal"
                app:fontFilePath="@string/Helvetica_Bold_Extra" />

            <TextView
                android:id="@+id/txt_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Contract Money is: Rs 0"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginTop="@dimen/dp10"
                android:textStyle="normal"
                app:fontFilePath="@string/Helvetica_Bold_Extra" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="start"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginBottom="10dp"
            >

            <CheckBox
                android:id="@+id/chk_agree"
                android:checked="true"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:text="I agree this rule"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


        </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp20"
                android:layout_marginBottom="@dimen/dp20"
                >

                <androidx.cardview.widget.CardView
                    android:id="@+id/btn_yes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardBackgroundColor="#d3d3d3"
                    app:cardCornerRadius="5dp"
                    android:layout_marginRight="10dp"
                    >

                    <RelativeLayout
                        android:id="@+id/rl_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingRight="20dp"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="cancel"
                            android:textAllCaps="false"
                            android:textColor="@color/black"
                            android:textSize="18sp"
                            app:fontFilePath="@string/Helvetica_Bold_Extra" />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/bt_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:cardBackgroundColor="#04963c"
                    app:cardCornerRadius="5dp"
                    android:layout_marginLeft="10dp"
                    >

                    <RelativeLayout
                        android:id="@+id/rl_confirm"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingRight="20dp"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Confirm"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            app:fontFilePath="@string/Helvetica_Bold_Extra" />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>


            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>