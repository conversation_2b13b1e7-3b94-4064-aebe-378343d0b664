<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/history_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/active_navcolor"
    android:visibility="gone"
    android:weightSum="5.5">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1.3"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5"
        android:singleLine="true"
        android:text="GameID"
        android:textColor="@color/color2"
        android:textStyle="bold" />


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1.49"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5"
        android:singleLine="true"
        android:text="Amt"
        android:textColor="@color/color2"
        android:textStyle="bold" />


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1.45"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5"
        android:singleLine="true"
        android:text="Bet"
        android:textColor="@color/color2"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_weight="1.15"
        android:gravity="start|center"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5"
        android:singleLine="true"
        android:text="Win Amount"
        android:textColor="@color/color2"
        android:textStyle="bold" />

    <!--                <TextView-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_width="match_parent"-->
    <!--                    android:layout_weight="1.1"-->
    <!--                    android:textStyle="bold"-->
    <!--                    android:textColor="@color/color2"-->
    <!--                    android:singleLine="true"-->
    <!--                    android:gravity="start|center"-->
    <!--                    android:layout_gravity="center"-->
    <!--                    android:paddingHorizontal="@dimen/dp10"-->
    <!--                    android:paddingVertical="@dimen/dp5"-->
    <!--                    android:text="Color" />-->

</LinearLayout>
