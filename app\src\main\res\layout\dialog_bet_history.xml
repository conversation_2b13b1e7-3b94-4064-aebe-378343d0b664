<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    style="@style/dialogParentStyle">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@id/lnr_box"
        android:layout_alignRight="@id/lnr_box"
        android:layout_alignTop="@id/lnr_box"
        android:layout_alignBottom="@id/lnr_box" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginBottom="@dimen/dp20">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp20"
                android:paddingBottom="@dimen/dp20">

                <!-- Bet History List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:layout_marginTop="15dp"
                    android:id="@+id/recyclerBetHistory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

                <!-- No Data Message -->
                <TextView
                    android:id="@+id/tvNoData"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="No recent bet history"
                    android:textColor="@color/colordullwhite"
                    android:textSize="14sp"
                    android:visibility="gone"
                    android:layout_marginTop="50dp" />

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

    <include layout="@layout/dialog_toolbar" />

</RelativeLayout>
