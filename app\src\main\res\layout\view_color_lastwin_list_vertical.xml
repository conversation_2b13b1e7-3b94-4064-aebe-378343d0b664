<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10"
        >

        <androidx.cardview.widget.CardView
            android:layout_weight="1.5"
            app:cardBackgroundColor="@color/gray"
            style="@style/colorLastWinHeadingbg">

            <TextView
                android:id="@+id/tvFiled1"
                android:textColor="@color/color2"
                style="@style/colorLastWinHeadingText"
                android:text="Gameid"
                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1.3"
            app:cardBackgroundColor="@color/gray"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled2"
                android:textColor="@color/color2"
                style="@style/colorLastWinHeadingText"
                android:text="Winnerid"

                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="0.7"
            app:cardBackgroundColor="@color/gray"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled3"
                style="@style/colorLastWinHeadingText"
                android:textColor="@color/color2"
                android:text="Number"
                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1"
            android:layout_height="@dimen/dp30"
            app:cardBackgroundColor="@color/gray"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled5"
                android:layout_width="@dimen/dp25"
                style="@style/colorLastWinHeadingText"
                android:textColor="@color/color2"
                android:text=""
                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1"
            app:cardBackgroundColor="@color/gray"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled4"
                style="@style/colorLastWinHeadingText"
                android:text="Number"
                android:textColor="@color/color2"
                />

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</LinearLayout>