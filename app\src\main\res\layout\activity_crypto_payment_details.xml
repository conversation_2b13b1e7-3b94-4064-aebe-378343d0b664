<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    tools:context=".Activity.Splashscreen">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rl_extra"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/triangle"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp30"
                android:layout_centerHorizontal="true"
                android:layout_above="@id/wheel"
                android:tint="@color/blue"
                android:layout_alignParentTop="true"
                android:layout_marginBottom="-30dp"
                app:srcCompat="@drawable/triangle"
                />

            <ImageView
                android:id="@+id/wheel"
                android:layout_width="@dimen/payu_dimen_200dp"
                android:layout_height="@dimen/payu_dimen_200dp"
                android:layout_alignParentTop="true"
                android:layout_centerInParent="true"
                android:layout_marginTop="@dimen/dp25"
                android:adjustViewBounds="true"
                android:rotation="-13"
                android:scaleType="centerInside"
                app:srcCompat="@drawable/wheel_discount" />

            <TextView
                android:id="@+id/resultTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/spinBtn"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:text="you will get 2% discount"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/Golder_yellow"
                android:textSize="@dimen/payu_dimen_20sp"
                android:textStyle="bold"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/spin_txt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/wheel"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:text="You can spin only after every 10 minutes"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_14dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <Button
                android:id="@+id/spinBtn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:layout_below="@id/spin_txt"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_gredient_white_border"
                android:text="SPIN"
                android:textColor="@color/white" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/imgback"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/back"
            android:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtChipsdetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/dp20"
                android:text=""
                android:textColor="@color/colordullwhite"
                android:textSize="20dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/qrcode"
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_200dp"
                android:src="@drawable/backside_teenpatti"
                android:visibility="visible" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp30"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="visible">

                <TextView
                    android:id="@+id/address"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="Data no available!"
                    android:textColor="@color/white"
                    android:textSize="12dp" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/copyaddress"
                        android:layout_width="@dimen/dp20"
                        android:layout_height="@dimen/dp20"
                        android:layout_marginLeft="@dimen/dp10"
                        android:src="@drawable/txtcopy" />

                </RelativeLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/pay_load"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:background="@color/colorPrimary"
                    android:text="Select Payment Method"
                    android:textAlignment="center"
                    android:textColor="@color/colordullwhite"
                    android:textSize="25dp"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <RadioGroup
                    android:id="@+id/radioAppChoice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:padding="@dimen/dp10">

                    <RadioButton
                        android:id="@+id/app_default"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="Default"
                        android:visibility="gone" />

                    <RadioButton
                        android:id="@+id/app_amazonpay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="AmazonPay" />

                    <RadioButton
                        android:id="@+id/app_bhim_upi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="BHIM UPI" />

                    <RadioButton
                        android:id="@+id/app_google_pay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Google Pay" />

                    <RadioButton
                        android:id="@+id/app_phonepe"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PhonePe"
                        android:visibility="gone" />

                    <RadioButton
                        android:id="@+id/app_paytm"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PayTm" />
                </RadioGroup>


            </LinearLayout>

            <ImageView
                android:id="@+id/imgPaynow"
                android:layout_width="200dp"
                android:layout_height="100dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:src="@drawable/paynow"
                android:visibility="gone" />

            <TextView
                android:id="@+id/btnDummyPay"
                android:layout_width="200dp"
                android:layout_height="50dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/red_button"
                android:gravity="center"
                android:paddingBottom="@dimen/dp5"
                android:text="Dummy Pay now"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:visibility="gone" />


        </LinearLayout>

    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>