<?xml version="1.1" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item android:right="5dp" android:top="0dp">
                <shape>
                    <corners android:radius="@dimen/dimen_15dp" />
                    <gradient
                        android:angle="45"
                        android:startColor="#8008c3fa"
                        android:endColor="#8008c3fa"
                        android:type="linear"
                        />
                </shape>
            </item>
            <item
                android:bottom="5dp"
                android:left="5dp"
                android:right="10dp"
                android:top="5dp">
                <shape xmlns:android="http://schemas.android.com/apk/res/android" android:shape="rectangle" >
                    <corners
                        android:radius="50dp"
                        />
                    <gradient
                        android:angle="90"
                        android:centerX="50%"
                        android:centerColor="@color/transparent"
                        android:startColor="#8008c3fa"
                        android:endColor="#8008c3fa"
                        android:type="linear"
                        />
                    <padding
                        android:left="0dp"
                        android:top="0dp"
                        android:right="0dp"
                        android:bottom="0dp"
                        />
                    <size
                        android:width="44dp"
                        android:height="44dp"
                        />
                    <stroke
                        android:width="2.5dp"
                        android:color="#08c3fa"
                        />
                </shape>
            </item>
        </layer-list>
    </item>

</selector>