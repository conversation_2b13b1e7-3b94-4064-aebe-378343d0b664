<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/relativeChip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <RelativeLayout
        android:id="@+id/rltmainview"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/dp10"
        android:layout_height="@dimen/dp90">

        <TextView
            android:id="@+id/txt_cat"
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp90"
            android:layout_centerInParent="true"
            android:background="@drawable/join_green_vertical"
            android:ellipsize="end"
            android:gravity="center"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_0dp"
            android:textStyle="bold" />

        <!--        <TextView-->
        <!--            android:id="@+id/chipCoin"-->
        <!--            android:layout_width="@dimen/dp40"-->
        <!--            android:layout_height="@dimen/dp40"-->
        <!--            android:layout_centerInParent="true"-->
        <!--            android:layout_marginStart="@dimen/dp7"-->
        <!--            android:layout_marginTop="@dimen/dp7"-->
        <!--            android:layout_marginEnd="@dimen/dp7"-->
        <!--            android:layout_marginBottom="@dimen/dp7"-->
        <!--            android:background="@drawable/ic_dt_chips"-->
        <!--            android:gravity="center"-->
        <!--            android:visibility="gone" />-->


        <RelativeLayout
            android:id="@+id/rltAddedChpisviewbig"
            android:layout_width="@dimen/dt_putchips_size"
            android:layout_height="@dimen/dt_putchips_size"
            android:layout_centerInParent="true"
            android:layout_marginRight="@dimen/dp10"
            android:background="@drawable/ic_dt_chips"
            android:visibility="gone">

        <TextView
            android:id="@+id/tvDragonCoins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:padding="3dp"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text=""
            android:textColor="@color/colorPrimary"
            android:textSize="10dp"
            android:textStyle="bold"
            android:visibility="visible" />

        </RelativeLayout>
    </RelativeLayout>

</LinearLayout>