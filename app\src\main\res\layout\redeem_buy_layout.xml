<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp">

    <ImageView
        android:id="@+id/imalucky"
        android:layout_width="120dp"
        android:layout_height="100dp"
        android:src="@drawable/bulkchipsgreen" />

    <Button
        android:id="@+id/btnCoinValue"
        android:layout_width="120dp"
        android:layout_height="32dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/ic_button"
        android:drawableStart="@drawable/gif_ruppe_"
        android:drawablePadding="2dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:gravity="center"
        android:text="10,000"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:textStyle="bold"
        android:ellipsize="none"
        android:maxLines="1"
        android:drawableTint="#FFFFFF" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Withdraw: "
            android:textColor="@color/Golder_yellow"
            android:textSize="12sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txtWithdrawAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10,000"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>