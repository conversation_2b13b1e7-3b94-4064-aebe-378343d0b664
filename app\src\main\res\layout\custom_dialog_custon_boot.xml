<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    style="@style/dialogParentStyle"    >

    <RelativeLayout
        android:id="@+id/dialog_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/txtYouhave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp"
                android:text="YOU HAVE CHIPS"
                android:textColor="#ffffff"
                android:textSize="20dp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txtYouhave"
                android:layout_centerHorizontal="true"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/imgchipps"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/chipp"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/txtwalletchips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="YOU HAVE CHIPS"
                    android:textColor="#ffffff"
                    android:textSize="20dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/txtStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/seekBar1"
                android:layout_marginLeft="15dp"
                android:layout_marginBottom="10dp"
                android:text="50"
                android:textColor="#ffffff"
                android:textSize="15dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/txtLimit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/seekBar1"
                android:layout_alignParentRight="true"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="10dp"
                android:text="250k"
                android:textColor="#ffffff"
                android:textSize="15dp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBar1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:indeterminate="false"
                android:max="1500"
                android:progress="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/seekBar1"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtBootamount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="Boot amount : 0"
                    android:textColor="#ffffff"
                    android:textSize="15dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtPotLimit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:text="Pot limit : 0"
                    android:textColor="#ffffff"
                    android:textSize="15dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnrmax"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/seekBar1"
                android:layout_alignParentRight="true"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtNumberofBlind"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:text="Number of Blinds : 4"
                    android:textColor="#ffffff"
                    android:textSize="15dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtMaximumbetvalue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="10dp"
                    android:text="Maximum Bet Value : 0"
                    android:textColor="#ffffff"
                    android:textSize="15dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lnrmax"
                android:layout_centerHorizontal="true">
                <ImageView
                    android:id="@+id/imgCreatetable"
                    android:layout_width="250dp"
                    android:layout_height="45dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/create_table_btn" />
                <ImageView
                    android:id="@+id/imgJointable"
                    android:layout_width="250dp"
                    android:layout_height="45dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/jointablebutton" />
                </LinearLayout>


        </RelativeLayout>


    </RelativeLayout>

    <include
        layout="@layout/dialog_toolbar"
        />

</RelativeLayout>