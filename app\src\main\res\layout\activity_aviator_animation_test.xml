<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black"
    android:padding="16dp">


    <!-- Custom Animation View -->
    <com.gamegards.gaming27._Aviator.AviatorAnimationView
        android:id="@+id/aviator_animation_view"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:layout_marginBottom="50dp" />
    <!-- Status Text -->
    <TextView
        android:id="@+id/txt_multiplier"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="@color/coloryellow"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:visibility="gone" />

</LinearLayout>
