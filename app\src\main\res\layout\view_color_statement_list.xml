<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp10"
        >
        <androidx.cardview.widget.CardView
            android:layout_weight=".6"
            app:cardBackgroundColor="@color/white"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled1"
                android:textColor="@color/red"
                style="@style/colorLastWinHeadingText"
                android:text="Gameid"
                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1.5"
            app:cardBackgroundColor="@color/white"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled2"
                android:textColor="@color/red"
                style="@style/colorLastWinHeadingText"
                android:text="Winnerid"

                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1.1"
            app:cardBackgroundColor="#F7F7F7"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled3"
                style="@style/colorLastWinHeadingText"
                android:textColor="@color/red"
                android:text="Number"
                />

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_weight="1.6"
            app:cardBackgroundColor="#F7F7F7"
            style="@style/colorLastWinHeadingbg"
            >

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvFiled4"
                style="@style/colorLastWinHeadingText"
                android:text="Number"
                android:textAlignment="center"
                android:layout_width="match_parent"
                android:textColor="@color/black"
                />
            <TextView
                android:id="@+id/tvFiled6"
                style="@style/colorLastWinHeadingText"
                android:text="500"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:textColor="@color/btn_green"
                />
            </LinearLayout>

        </androidx.cardview.widget.CardView>
        <androidx.cardview.widget.CardView
            android:layout_weight="1.8"
            app:cardBackgroundColor="#F7F7F7"
            style="@style/colorLastWinHeadingbg"
            >

            <TextView
                android:id="@+id/tvFiled5"
                style="@style/colorLastWinHeadingText"
                android:text="Number"
                android:textColor="@color/red"
                />

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</LinearLayout>