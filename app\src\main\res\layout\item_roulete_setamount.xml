<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    >

    <RelativeLayout
        android:id="@+id/rltContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_cat_BetAmount"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:background="@drawable/ic_dt_chips"
            android:translationZ="90dp"
            android:text=""
            android:textSize="8sp"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivContainerbg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/lnrJackportamountparent"
            android:layout_alignTop="@id/lnrJackportamountparent"
            android:layout_alignRight="@id/lnrJackportamountparent"
            android:layout_alignBottom="@id/lnrJackportamountparent"
            android:background="@drawable/d_trans_ractangle_white" />

        <LinearLayout
            android:id="@+id/lnrJackportamountparent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp35"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:visibility="gone"
                    >

                    <TextView
                        android:id="@+id/tvJackpotamount"
                        style="@style/ShadowWhiteTextview"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0000"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="2dp"
                        android:background="?dividerHorizontal" />

                    <TextView
                        android:id="@+id/tvJackpotSelectamount"
                        style="@style/ShadowWhiteTextview"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <TextView
                    android:id="@+id/btninto"
                    style="@style/ShadowGoldTextview"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="@dimen/dp15"
                    android:gravity="center"
                    android:text="2x"
                    android:textSize="10sp"
                    android:visibility="gone"
                    />
            </RelativeLayout>

            <TextView
                android:id="@+id/tvJackpotHeading"
                style="@style/roulatteBoadPutBetTextview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/dp5"
                android:gravity="center"
                android:paddingTop="3dp"
                android:text="SET"
                android:textAllCaps="true"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rltAmountadded"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerHorizontal="true"
            android:layout_below="@+id/lnrJackportamountparent"
            android:layout_marginTop="-25dp"
            android:visibility="gone"
            >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_hightlight"
                android:layout_alignRight="@id/tvUsersAddedAmount"
                android:layout_alignLeft="@id/tvUsersAddedAmount"
                android:layout_alignTop="@id/tvUsersAddedAmount"
                android:layout_alignBottom="@id/tvUsersAddedAmount"
                android:alpha="0.4"
                />

            <TextView
                android:id="@+id/tvUsersAddedAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                style="@style/ShadowGoldTextview"
                android:textSize="18sp"
                android:paddingRight="10dp"
                android:paddingLeft="10dp"
                />

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>