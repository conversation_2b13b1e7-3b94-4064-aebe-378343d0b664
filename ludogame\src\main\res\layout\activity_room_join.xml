<?xml version="1.1" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.first_player_games.OnlineGame.Lobby.RoomJoinActivity">

    <ImageView
        android:id="@+id/img1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:src="@drawable/background_new"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleType="centerCrop"
        />

    <TextView
        android:id="@+id/heading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:fontFamily="@font/mama_bear"
        android:text="Room Join"
        android:textColor="@color/white"
        android:textSize="25dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/adView"
        android:layout_marginTop="20dp"
        />

    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        app:adSize="BANNER"
        app:adUnitId="@string/banner_ad_unit_id"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Roomid"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationroomid"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        />
    <TextView
        android:id="@+id/roomcreationroomid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="Please wait"
        android:textColor="@color/white"
        android:textSize="25dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationjoinedplayersview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/heading"
        app:layout_constraintVertical_bias="0.4" />

    <ImageView
        android:id="@+id/img_dice"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:paddingVertical="5dp"
        android:paddingRight="5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_dice"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/roomcreationroomid"
        app:tint="@color/white" />

    <ImageButton
        android:id="@+id/imageButton4"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/rounded_shape"
        android:backgroundTint="@color/yellow3"
        android:paddingVertical="5dp"
        android:paddingRight="5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_share"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/roomcreationroomid"
        app:tint="@color/purple" />

    <TextView
        android:id="@+id/diamonds_amount_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/mama_bear"
        android:text="Winner gets 10 Diamonds"
        android:textColor="@color/white"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationjoinedplayersview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageButton4" />

    <LinearLayout
        android:id="@+id/roomcreationjoinedplayersview"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:orientation="vertical"
        android:paddingHorizontal="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_margin="20dp"
        android:paddingTop="20dp"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:fontFamily="@font/mama_bear"
            android:text="Players Joined"
            android:textColor="@color/yellow3" />

        <LinearLayout
            android:id="@+id/roomcreation_player_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="invisible"
            >

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/red1" />

            <TextView
                android:id="@+id/roomcreation_player_name_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/roomcreation_player_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="invisible"
            >

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/blue1" />

            <TextView
                android:id="@+id/roomcreation_player_name_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/roomcreation_player_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="invisible"
            >

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/yellow1" />

            <TextView
                android:id="@+id/roomcreation_player_name_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/roomcreation_player_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            >

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/gree1" />

            <TextView
                android:id="@+id/roomcreation_player_name_4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>