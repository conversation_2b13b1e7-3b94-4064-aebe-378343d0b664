<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    style="@style/dialogParentStyle"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        >

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            style="@style/popUpBoxbg"
            android:orientation="vertical"
            android:id="@+id/lnr_box"
            android:layout_marginTop="@dimen/pop_up_top_margin"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_marginHorizontal="25dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_marginVertical="30dp"
                android:layout_marginHorizontal="25dp"
                >

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Select a gift to send"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_marginBottom="15dp"
                    />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recylerview_gifts"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:paddingVertical="8dp"
                    android:clipToPadding="false"
                    />

            </LinearLayout>

            <TextView
                android:id="@+id/txtnotfound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="No gifts available at the moment!"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:gravity="center"
                android:visibility="gone"
                android:drawableTop="@drawable/gift_boxnew"
                android:drawablePadding="16dp"
                android:padding="32dp"
                />

            <RelativeLayout
                android:id="@+id/rlt_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="300dp"
                    android:layout_height="100dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:indeterminateDrawable="@drawable/cpb_3"
                    android:indeterminateDuration="4000"
                    android:visibility="visible" />
            </RelativeLayout>





        </RelativeLayout>


        <include
            layout="@layout/dialog_toolbar"/>

    </RelativeLayout>



</RelativeLayout>