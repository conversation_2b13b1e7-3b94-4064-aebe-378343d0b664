package com.example.sms_readpayment;

import androidx.appcompat.app.AppCompatActivity;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.example.sms_readpayment.ApiClasses.ApiLinks;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


public class InitiatePayment extends AppCompatActivity {

    EditText et_amt, et_upi_id, et_user_id, et_name, et_note;
    Button btn_submit;
    final int UPI_PAYMENT = 0;
    final int UPI_PAYMENT_via = 2;
    Handler handler = new Handler();
    Runnable runnable;
    int delay = 10 * 1000; //Delay for 15 seconds.  One second = 1000 milliseconds.
    TextView txt_title;
    TranslateAnimation animate;

    String str_order_id = "", str_user_id = "", str_amt = "", str_upi = "", str_merchant_id = "", str_merchant_secret = "", str_user_name = "", param1 = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_initiate_payment);

        et_amt = findViewById(R.id.et_amt);
        et_upi_id = findViewById(R.id.et_upi_id);
        et_user_id = findViewById(R.id.et_user_id);
        et_name = findViewById(R.id.et_name);
        et_note = findViewById(R.id.et_note);
        txt_title = findViewById(R.id.txt_title);
        btn_submit = findViewById(R.id.btn_submit);

        animate = new TranslateAnimation(11, -11, 0, 0);
        animate.setDuration(1000);
        animate.setFillAfter(true);
        animate.setRepeatMode(2);
        animate.setRepeatCount(Animation.INFINITE);
        txt_title.startAnimation(animate);

        if (getIntent().hasExtra("amt") && getIntent().hasExtra("upi") && getIntent().hasExtra("merchant_id") &&
                getIntent().hasExtra("merchant_secret") && getIntent().hasExtra("user_id") && getIntent().hasExtra("name") && getIntent().hasExtra("param1")) {
            str_amt = getIntent().getStringExtra("amt");
            str_upi = getIntent().getStringExtra("upi");
            str_merchant_id = getIntent().getStringExtra("merchant_id");
            str_merchant_secret = getIntent().getStringExtra("merchant_secret");
            str_user_id = getIntent().getStringExtra("user_id");
            str_user_name = getIntent().getStringExtra("name");
            str_order_id = getIntent().getStringExtra("param1");
            btn_submit.setEnabled(true);
        } else {
            Toast.makeText(this, "Parameter missing..", Toast.LENGTH_SHORT).show();
            btn_submit.setEnabled(false);
        }


      /*  (findViewById(R.id.pay_via_upi)).setOnClickListener(view -> {
            Uri uri = Uri.parse("upi://pay?pa=" + et_upi_id.getText().toString() + "&pn=Info nix Data Private Limited&mc=5816&tid=AXIFRCO120720221js3b5g8ft&tr=AXIFRCO120720221js3b5g8ft&am=+" + et_amt.getText().toString() + "+&cu=INR\"");
            Intent upiPayIntent = new Intent(Intent.ACTION_VIEW);
            upiPayIntent.setData(uri);
            Intent chooser = Intent.createChooser(upiPayIntent, "Pay with");
            if (null != chooser.resolveActivity(getPackageManager())) {
                startActivityForResult(chooser, UPI_PAYMENT_via);
            } else {
                Toast.makeText(getApplicationContext(), "No UPI app found, please install one to continue", Toast.LENGTH_SHORT).show();
            }

        });*/

        btn_submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (et_upi_id.getText().toString().equals("")) {
                    Toast.makeText(InitiatePayment.this, "Please enter valid UPI Id", Toast.LENGTH_SHORT).show();
                } else {
                    initiate_payment();
                }

            }
        });

    }

    String payment_id = "", payment_upi = "", payment_amt = "", payment_url = "";

    public void initiate_payment() {
        Log.d("USDT_PAYMENT", "=== STATIC QR CODE PAYMENT ===");
        Log.d("USDT_PAYMENT", "Showing static USDT wallet directly - no API calls");

        // Directly use static wallet data - no API calls, no merchant authentication
        String staticWalletAddress = "******************************************";

        // Store wallet information for payment processing
        payment_id = staticWalletAddress; // Use wallet address as payment ID
        payment_upi = staticWalletAddress; // Store wallet address
        payment_amt = et_amt.getText().toString().trim(); // Use the amount entered
        payment_url = ""; // Not needed for USDT payments

        if (payment_amt.isEmpty()) {
            payment_amt = "10.00"; // Default amount
        }

        Log.d("USDT_PAYMENT", "Static wallet address: " + staticWalletAddress);
        Log.d("USDT_PAYMENT", "Payment amount: " + payment_amt);

        Toast.makeText(InitiatePayment.this, "USDT Wallet Ready - Showing QR Code", Toast.LENGTH_SHORT).show();

        // Go directly to payment screen
        call_success();
    }

    void payUsingUpi(String pay_url) {
        Log.e("payUsingUpi", "payUsingUpi: " + pay_url);
      /*  Uri uri = Uri.parse("upi://pay").buildUpon()
                .appendQueryParameter("pa", "q86747985@ybl")
                .appendQueryParameter("pn", "Anil Stores")
                .appendQueryParameter("tn", "note")
                .appendQueryParameter("am", "2")
               *//* .appendQueryParameter("mc", "5411")
                .appendQueryParameter("tid", "AXIFRCO110720255mmiva1a4o")
                .appendQueryParameter("tr", "AXIFRCO110720255mmiva1a4o")*//*
                .appendQueryParameter("cu", "INR")
                .build();*/

        //Log.e("uri_upi", "payUsingUpi: " + uri);
        Intent upiPayIntent = new Intent(Intent.ACTION_VIEW);
        upiPayIntent.setData(Uri.parse(pay_url));
        //upiPayIntent.setData(uri);
        Intent chooser = Intent.createChooser(upiPayIntent, "Pay with");

        // check if intent resolves
        if (null != chooser.resolveActivity(getPackageManager())) {
            startActivityForResult(chooser, UPI_PAYMENT);
        } else {
            Toast.makeText(InitiatePayment.this, "No UPI app found, please install one to continue", Toast.LENGTH_SHORT).show();
        }

    }

    public void call_success() {
        Intent i = new Intent(InitiatePayment.this, CheckPaymentStatus.class);
        i.putExtra("payment_id", payment_id);
        i.putExtra("amount", payment_amt);
        i.putExtra("wallet_address", payment_upi); // Pass wallet address
        i.putExtra("upi_id_entered", ""); // No UPI ID for USDT payments
        i.putExtra("payment_url", payment_url);
        i.putExtra("user_id", str_user_id);
        i.putExtra("payment_type", "USDT"); // Indicate this is a USDT payment
        i.putExtra("network", "BEP20"); // Default network

        Log.d("USDT_PAYMENT", "Navigating to CheckPaymentStatus with wallet: " + payment_upi);
        Log.d("USDT_PAYMENT", "Payment amount: " + payment_amt);

        startActivity(i);
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == UPI_PAYMENT) {
            if ((RESULT_OK == resultCode) || (resultCode == 11)) {
                if (data != null) {
                    String trxt = data.getStringExtra("response");
                    Log.d("UPI", "onActivityResult: " + trxt);
                    Log.d("_data_", String.valueOf(data));
                    ArrayList<String> dataList = new ArrayList<>();
                    dataList.add(trxt);
                    upiPaymentDataOperation(dataList);
                } else {
                    Log.d("UPI", "onActivityResult: " + "Return data is null");
                    ArrayList<String> dataList = new ArrayList<>();
                    dataList.add("nothing");
                    upiPaymentDataOperation(dataList);
                }
            } else {
                Log.d("UPI", "onActivityResult: " + "Return data is null"); //when user simply back without payment
                ArrayList<String> dataList = new ArrayList<>();
                dataList.add("nothing");
                upiPaymentDataOperation(dataList);
            }
        } else if (requestCode == UPI_PAYMENT_via) {
            if ((RESULT_OK == resultCode)) {
                if (data != null) {
                    String trxt = data.getStringExtra("response");
                    Log.d("UPI", "onActivityResult: " + trxt);
                    Log.d("_data_", String.valueOf(data));
                }
            }
        }
    }

    private void upiPaymentDataOperation(ArrayList<String> data) {
        if (isConnectionAvailable(InitiatePayment.this)) {
            String str = data.get(0);
            Log.d("UPIPAY", "upiPaymentDataOperation: " + str);
            String paymentCancel = "";
            if (str == null) str = "discard";
            String status = "";
            String approvalRefNo = "";
            String response[] = str.split("&");
            for (int i = 0; i < response.length; i++) {
                String equalStr[] = response[i].split("=");
                if (equalStr.length >= 2) {
                    if (equalStr[0].toLowerCase().equals("Status".toLowerCase())) {
                        status = equalStr[1].toLowerCase();
                    } else if (equalStr[0].toLowerCase().equals("ApprovalRefNo".toLowerCase()) || equalStr[0].toLowerCase().equals("txnRef".toLowerCase())) {
                        approvalRefNo = equalStr[1];
                    }
                } else {
                    //paymentCancel = "Payment cancelled by user.";
                }
            }

            Log.d("status_", status);
            if (status.equals("success")) {
                //Code to handle successful transaction here.
                Toast.makeText(InitiatePayment.this, "Transaction successful.", Toast.LENGTH_SHORT).show();
                Log.d("UPI_payment", "responseStr: " + approvalRefNo);
            } else if ("Payment cancelled by user.".equals(paymentCancel)) {
                Toast.makeText(InitiatePayment.this, "Payment cancelled by user.", Toast.LENGTH_SHORT).show();
                Log.d("payment_cancelled", "cancel");
            } else {
                //Toast.makeText(InitiatePayment.this, "Transaction failed.Please try again", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(InitiatePayment.this, "Internet connection is not available. Please check and try again", Toast.LENGTH_SHORT).show();
        }
        if (!payment_id.equals("")) {
            call_success();
        } else {
            Toast.makeText(this, "Payment id is empty!", Toast.LENGTH_SHORT).show();
        }

//        check();

    }

    private void check_status() {
        ProgressDialog progressDialog;
        progressDialog = new ProgressDialog(InitiatePayment.this);
        progressDialog.setMessage("Checking...");
        progressDialog.setCancelable(true);
        progressDialog.show();
        if (ApiLinks.isNetworkAvailable(InitiatePayment.this)) {
            StringRequest stringRequest = new StringRequest(Request.Method.POST, "",
                    new Response.Listener<String>() {
                        @Override
                        public void onResponse(String response) {
                            try {
                                JSONObject jsonObject = new JSONObject(response);
//                                AppHelper.LogCat(response);
                                Log.d("payment_status", response);
                                String code = jsonObject.getString("code");
                                String message = jsonObject.getString("message");
//                                payment_id = jsonObject.getString("payment_id");

                                if (code.equals("200") && message.equals("Success")) {
                                    progressDialog.dismiss();
                                    handler.removeCallbacks(runnable);
                                    Toast.makeText(InitiatePayment.this, "Thank you for your Payment!", Toast.LENGTH_SHORT).show();

                                } else {
                                    progressDialog.dismiss();
                                    Toast.makeText(InitiatePayment.this, "" + message, Toast.LENGTH_SHORT).show();
                                }

                            } catch (Exception e) {
                                progressDialog.dismiss();
                                e.printStackTrace();
                            }
                        }
                    }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    progressDialog.dismiss();
                    error.printStackTrace();

                }
            }) {
                @Override
                public Map<String, String> getHeaders() throws AuthFailureError {
                    HashMap<String, String> header = new HashMap<>();
                    header.put("token", ApiLinks.Token);
                    return header;
                }

                @Override
                protected Map<String, String> getParams() throws AuthFailureError {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("payment_id", payment_id);
//                    params.put("sms",sms_text);
                    Log.d("data", "getParams1: " + params);
                    return params;
                }
            };

            stringRequest.setRetryPolicy(new DefaultRetryPolicy(
                    0,
                    0,
                    DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));
            Volley.newRequestQueue(InitiatePayment.this).add(stringRequest);

        } else {
            errMsg("check your internet connection...");
        }
    }

    public void check() {
        handler.postDelayed(runnable = new Runnable() {
            public void run() {
                //do something
                check_status();
                handler.postDelayed(runnable, delay);
            }
        }, delay);
    }

    public static boolean isConnectionAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo netInfo = connectivityManager.getActiveNetworkInfo();
            if (netInfo != null && netInfo.isConnected()
                    && netInfo.isConnectedOrConnecting()
                    && netInfo.isAvailable()) {
                return true;
            }
        }
        return false;
    }

    private boolean errMsg(String msg) {
        Toast.makeText(InitiatePayment.this, "" + msg, Toast.LENGTH_SHORT).show();
        return false;
    }
}