<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgoundfragmend"
    tools:context=".GameChatActivity">

    <!-- Fixed Header -->
    <LinearLayout
        android:id="@+id/lnr_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="15dp"
        android:gravity="center_vertical"
        android:background="@color/backgoundfragmend"
        android:elevation="4dp">

        <ImageView
            android:id="@+id/imgclose"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Game Table Chat"
            android:textSize="18sp"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:layout_marginLeft="12dp"
            android:gravity="center_vertical" />

    </LinearLayout>

    <!-- Scrollable Messages Area -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recylerview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/backgoundfragmend"
        android:paddingHorizontal="12dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:clipToPadding="false"
        android:scrollbars="vertical"
        android:fadeScrollbars="true" />

    <!-- Fixed Bottom Input Area -->
    <LinearLayout
        android:id="@+id/lnr_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:background="@color/backgoundfragmend"
        android:elevation="4dp">

        <EditText
            android:id="@+id/edtText"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:hint="Type your message..."
            android:textColor="@android:color/black"
            android:textColorHint="@android:color/darker_gray"
            android:background="@drawable/raounded_order_blank"
            android:paddingHorizontal="16dp"
            android:paddingVertical="12dp"
            android:textSize="14sp"
            android:maxLines="3"
            android:inputType="textMultiLine|textCapSentences"
            android:imeOptions="actionSend"
            android:layout_marginRight="8dp" />

        <ImageView
            android:id="@+id/btnSendMessage"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/chaticon"
            android:rotation="-15"
            android:padding="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:scaleType="centerInside" />

    </LinearLayout>






</LinearLayout>