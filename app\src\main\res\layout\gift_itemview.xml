<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardUseCompatPadding="true"
    app:cardBackgroundColor="@android:color/transparent"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/gift_bg"
        android:id="@+id/cd_items"
        android:foreground="?selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        >

        <TextView
            android:id="@+id/txtgiftname"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Gift Name"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_gravity="center"
            android:gravity="center"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="8dp"
            android:layout_marginHorizontal="4dp"
            />

        <ImageView
            android:id="@+id/imggift"
            android:layout_width="75dp"
            android:layout_height="75dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_gravity="center"
            android:src="@drawable/gift_boxnew"
            android:padding="4dp"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"
            />

        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0 Coins"
            android:textColor="@color/yellow"
            android:textSize="11sp"
            android:layout_gravity="center"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:layout_marginHorizontal="4dp"
            android:background="@drawable/rounded_shape"
            android:paddingVertical="2dp"
            />


    </LinearLayout>

</androidx.cardview.widget.CardView>