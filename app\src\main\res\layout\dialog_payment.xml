<?xml version="1.1" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp">

    <RelativeLayout
        android:layout_width="350dp"
        android:layout_height="wrap_content"
        android:padding="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/payu_dimen_55dp"
                android:layout_marginBottom="20dp"
                android:paddingLeft="10dp"
                android:paddingTop="8dp"
                android:paddingRight="10dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/tv_heading"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:paddingLeft="@dimen/dp7"
                    android:paddingTop="@dimen/dp7"
                    android:text="Select Payment Option"
                    android:textAllCaps="true"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    app:fontFilePath="@string/Helvetica_Bold_Extra" />

                <ImageView
                    android:id="@+id/ivClose"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_close_new" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="-20dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal">

                <androidx.cardview.widget.CardView
                    android:visibility="visible"
                    android:id="@+id/btn_yes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/dp10"
                    android:layout_marginRight="10dp"
                    android:background="@color/Golder_yellow"
                    app:cardCornerRadius="5dp">

                    <RelativeLayout
                        android:layout_width="140dp"
                        android:layout_height="@dimen/payu_dimen_45dp"
                        android:background="#F1CA1F"
                        android:paddingLeft="20dp"
                        android:paddingTop="8dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="8dp">

                        <TextView
                            android:id="@+id/yes"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="Automatic"
                            android:textAllCaps="true"
                            android:textColor="@color/black"
                            android:textSize="15sp"
                            app:fontFilePath="@string/Helvetica_Bold_Extra" />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/bt_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="25dp"
                    app:cardCornerRadius="5dp">

                    <RelativeLayout
                        android:layout_width="140dp"
                        android:layout_height="@dimen/payu_dimen_45dp"
                        android:paddingLeft="20dp"
                        android:background="#F1CA1F"
                        android:paddingRight="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:layout_centerInParent="true"
                            android:text="Manual"
                            android:textAllCaps="true"
                            android:textColor="@color/black"
                            android:textSize="15sp"
                            app:fontFilePath="@string/Helvetica_Bold_Extra" />


                    </RelativeLayout>

                </androidx.cardview.widget.CardView>


            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>