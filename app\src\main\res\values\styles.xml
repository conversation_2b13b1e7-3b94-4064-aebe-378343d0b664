<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowBackground">@null</item>

        <item name="android:editTextStyle">@style/EditTextStyle</item>
        <item name="android:buttonStyle">@style/ButtonStyle</item>
        <item name="android:textViewStyle">@style/TextViewStyle</item>
        <item name="android:radioButtonStyle">@style/RadioStyle</item>
        <item name="fontFamily">@font/poppins_regular</item>

        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>

    </style>
    <style name="user_coin_bg">
        <item name="android:layout_width">@dimen/dp85</item>
        <item name="android:layout_height">@dimen/dp22</item>
    </style>
    <style name="EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="ButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="TextViewStyle" parent="Widget.AppCompat.TextView">
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="RadioStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:fontFamily">@font/poppins_regular</item>
    </style>

    <style name="AlertDialogCustom" parent="Theme.AppCompat.Light.Dialog.Alert">

    </style>

    <style name="DialogTheme">
        <item name="android:windowEnterAnimation">@anim/slide_left</item>
        <item name="android:windowExitAnimation">@anim/slide_right</item>
    </style>

    <style name="EditTextWithBackground">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textColorHint">@color/black</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/raounded_order_blank</item>
        <item name="android:inputType">text</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:layout_marginVertical">5dp</item>
        <item name="android:layout_marginHorizontal">5dp</item>
        <item name="android:elevation">5dp</item>
    </style>

    <style name="BlackTextview">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:text">0</item>
        <item name="android:layout_marginLeft">20dp</item>
    </style>

    <style name="PlayerWalletTextview">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:text">0</item>
    </style>

    <style name="ShadowWhiteTextview">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="UserNameTextStyle">
        <item name="android:layout_width">70dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginLeft">5dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_up</item>
    </style>

    <!--  Transparent dialog -->
    <style name="TransparentProgressDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@color/transparent</item>
    </style>

    <!--BottomSheetDialog-->
    <style name="SheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:colorBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
    </style>


    <style name="HomeIconTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gold_color</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="ShadowGoldTextview">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/Golder_yellow</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="ShadowGoldTextviewNew">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/Golder_yellow</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="UserStatusCirclrTextview">
        <item name="android:layout_width">55dp</item>
        <item name="android:layout_height">55dp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:background">@drawable/black_transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:text">@string/pending</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="TableHeadingTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/Golder_yellow</item>
        <item name="android:textSize">@dimen/table_heading_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="TableValueTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/table_value_color</item>
        <item name="android:textSize">@dimen/table_text_size</item>
        <item name="android:textStyle">normal</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:text">000</item>
    </style>

    <style name="GameHeadingTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gold</item>
        <item name="android:textSize">24sp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textStyle">bold</item>
    </style>

    <string name="pending">Pending</string>

    <style name="RummDetailsText">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">4dp</item>
    </style>

    <style name="carRoulateIconSelectionStyle">
        <item name="android:layout_width">@dimen/payu_dimen_45dp</item>
        <item name="android:layout_height">@dimen/payu_dimen_45dp</item>
        <item name="android:visibility">gone</item>
        <item name="android:background">@drawable/ic_carroulate_selectedbg</item>
    </style>

    <style name="carRoulateIconStyle">
        <item name="android:layout_width">@dimen/dp35</item>
        <item name="android:layout_height">@dimen/dp35</item>
        <item name="android:layout_centerInParent">true</item>
    </style>

    <style name="teenpattiSlideshowText">
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:background">@drawable/black_transparent</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">@dimen/TeenpattislideshowText</item>
        <item name="android:visibility">gone</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="closeButton">
        <item name="android:layout_width">24dp</item>
        <item name="android:layout_height">24dp</item>
        <item name="android:src">@drawable/close</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_marginTop">@dimen/dimen_35dp</item>
        <item name="android:layout_marginRight">@dimen/dp50</item>
    </style>

    <style name="popUpBoxbg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@drawable/home_bg2</item>
    </style>
    <style name="popUpBoxbgProfile">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@drawable/home_bg2</item>
    </style>

    <style name="dialogParentStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@drawable/home_bg2</item>
    </style>
    <style name="EditTextWithBackground_new">
        <item name="android:layout_width">@dimen/dp300</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textColorHint">@color/black</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/raounded_order_blank</item>
        <item name="android:inputType">text</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:layout_marginVertical">5dp</item>
        <item name="android:layout_marginHorizontal">5dp</item>
        <item name="android:elevation">5dp</item>
    </style>
    <style name="profileFieldTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:text">-</item>
    </style>

    <style name="profileFieldValueTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/Golder_yellow</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:text">-</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">right</item>
    </style>

    <style name="profileFieldParent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:background">@drawable/ic_purple_strock</item>
        <item name="android:layout_marginBottom">@dimen/dp10</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
    </style>

    <style name="packageRaiseButtonStyle">
        <item name="android:layout_width">100dp</item>
        <item name="android:layout_height">35dp</item>
        <item name="android:background">@drawable/btn_blue_declare</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:elevation">5dp</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="packageRaisePriceButtonStyle">
        <item name="android:layout_width">100dp</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:background">@drawable/ic_btn_blind</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:layout_marginTop">-12dp</item>
        <item name="android:paddingTop">10dp</item>
    </style>

    <style name="PokerPlayerRoleTextview">
        <item name="android:layout_width">22dp</item>
        <item name="android:layout_height">22dp</item>
        <item name="android:background">@drawable/circle</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
        <item name="android:gravity">center</item>
        <item name="android:text">B</item>
    </style>


    <style name="colorLastWinHeadingText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:text">Period</item>
        <item name="android:paddingHorizontal">@dimen/dp10</item>
        <item name="android:paddingVertical">@dimen/dp5</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:singleLine">false</item>
    </style>

    <style name="colorLastWinHeadingbg" parent="Widget.MaterialComponents.CardView">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="cardCornerRadius">@dimen/dp5</item>
        <item name="cardElevation">0dp</item>
        <item name="android:layout_marginLeft">5dp</item>
    </style>

    <style name="roulatteBoadPutBetTextview">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">3</item>
        <item name="android:paddingHorizontal">2dp</item>
    </style>

    <style name="RummyActionButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:background">@drawable/d_btn_brown</item>
    </style>

    <style name="RummyActionButton_drop">
        <item name="android:layout_width">@dimen/dp90</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:layout_marginRight">10dp</item>
    </style>
    <style name="RummyActionButton_finish_new">
        <item name="android:layout_width">@dimen/dp80</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:layout_marginRight">10dp</item>

    </style>
</resources>
