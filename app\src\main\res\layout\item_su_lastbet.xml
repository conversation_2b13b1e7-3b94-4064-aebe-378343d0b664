<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@id/tvItems"
        android:layout_alignRight="@id/tvItems"
        android:layout_alignBottom="@id/tvItems"
        android:layout_alignTop="@id/tvItems"
        android:background="@drawable/ic_bg_serven_up_item"
        />
    <TextView
        android:id="@+id/tvItems"
        style="@style/ShadowWhiteTextview"
        android:layout_width="wrap_content"
        android:minWidth="25dp"
        android:layout_height="25dp"
        android:paddingRight="5dp"
        android:paddingLeft="5dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:gravity="center"
        android:text="Set"
        android:textSize="9sp"
        android:textStyle="bold"
        android:layout_marginRight="3dp"
        />
</RelativeLayout>