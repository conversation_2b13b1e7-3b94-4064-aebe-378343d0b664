<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    style="@style/dialogParentStyle"
    >


    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        style="@style/popUpBoxbg"
        android:layout_alignLeft="@id/lnr_box"
        android:layout_alignRight="@id/lnr_box"
        android:layout_alignTop="@id/lnr_box"
        android:layout_alignBottom="@id/lnr_box"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginBottom="@dimen/dp20"
            >

            <LinearLayout
                android:id="@+id/lnrRuleslist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp20"
                android:paddingBottom="@dimen/dp20"
                >


                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
** Games Rules **\n\n
"
                    android:textAlignment="center"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="

0 or 1 Symbols hit                                      lose
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
2 Symbols hit                                      win = bet * 1
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
3 Symbols hit                                      win = bet * 4
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
4 Symbols hit                                      win = bet * 5
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
5 Symbols hit                                      win = bet * 7
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
6 Symbols hit                                      win = bet * 10
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>


        </ScrollView>

    </LinearLayout>


    <include
        layout="@layout/dialog_toolbar"/>


</RelativeLayout>