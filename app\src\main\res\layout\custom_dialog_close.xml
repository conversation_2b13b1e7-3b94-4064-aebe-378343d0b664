<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/home_bg2">

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:src="@drawable/ic_close_new"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="500dp"
            android:layout_height="90dp"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:weightSum="4"

            >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/btnclose"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:src="@drawable/close_game"
                    android:text=" Close " />

                <TextView
                    android:id="@+id/txtClose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="Close"
                    android:textColor="@color/colordullwhite"
                    android:textSize="15dp" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/btnexitloby"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:src="@drawable/backtoloby"
                    android:text=" Close " />

                <TextView
                    android:id="@+id/txtexitLogy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="Back to Lobby"
                    android:textColor="@color/colordullwhite"
                    android:textSize="15dp" />


            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/btnexitgame"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:src="@drawable/exit_game"

                    />

                <TextView
                    android:id="@+id/txtExit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text=" Exit Game "
                    android:textColor="@color/colordullwhite"
                    android:textSize="15dp" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnr_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/btnswitchtabel"
                    android:layout_width="80dp"
                    android:layout_height="80dp"

                    android:layout_gravity="center_horizontal"
                    android:layout_weight="1"
                    android:src="@drawable/switch_table"

                    />

                <TextView
                    android:id="@+id/txtSwtch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text=" Switch Table "
                    android:textColor="@color/colordullwhite"
                    android:textSize="15dp" />

            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>