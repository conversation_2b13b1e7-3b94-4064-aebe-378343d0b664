<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    android:gravity="center"
    android:paddingLeft="0dp"
    android:paddingTop="0dp"
    android:paddingRight="0dp"
    android:paddingBottom="0dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="._LuckJackpot.LuckJackPot_A">

    <ImageView
        android:id="@+id/imgback"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/back"
        android:visibility="visible" />


    <ImageView
        android:id="@+id/ivHelp"
        android:layout_width="@dimen/dimen_40dp"
        android:layout_height="@dimen/dimen_40dp"
        android:layout_below="@+id/imgback"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/dp10"
        android:layout_marginTop="@dimen/dimen_10dp"
        android:onClick="openGameRules"
        android:src="@drawable/ic_jackpot_info"
        android:visibility="visible" />


    <include
        android:id="@+id/lnrtypegame"
        layout="@layout/view_user_bottom_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

<!--    <com.gamegards.letsplaycard.Utils.GifImageView-->
<!--        android:id="@+id/imgampire"-->
<!--        android:layout_width="@dimen/dimen_100dp"-->
<!--        android:layout_height="@dimen/dimen_100dp"-->
<!--        android:layout_centerHorizontal="true"-->
<!--        android:src="@drawable/girl_char_seven"-->
<!--        android:visibility="visible" />-->

    <RelativeLayout
        android:id="@+id/rl_table_main"
        android:layout_width="550dp"
        android:layout_height="@dimen/dp300"
        android:layout_centerInParent="true"
        >

        <RelativeLayout
            android:id="@+id/lnrCardsView"
            android:layout_width="match_parent"
            android:translationZ="90dp"
            android:layout_height="wrap_content"
            >



            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:paddingTop="20dp"
                android:visibility="visible">


                <TextView
                    android:id="@+id/txtcountdown"
                    style="@style/ShadowWhiteTextview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:text=""
                    android:textColor="#EEC283"
                    android:textSize="20dp"
                    android:textStyle="bold"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/txtGameBets"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:src="@drawable/place_your_bet"
                    android:visibility="gone" />
            </RelativeLayout>
        </RelativeLayout>
        <!--// Also change from table image if changing-->

        <ImageView
            android:id="@+id/imgTable"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:background="@drawable/ic_jackpot_box_new"
            android:visibility="visible" />


        <RelativeLayout
            android:id="@+id/rtlGameContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="-5dp"
            android:layout_centerInParent="true"
            >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/lnrtopviewcontainer"
                    android:layout_marginTop="5dp"
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/dp50"
                    android:paddingTop="5dp"
                    android:layout_marginEnd="@dimen/dp50">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rec_rules"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/dp15"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_jhandimunda_setamount" />


                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/ChipstoUser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignLeft="@id/txtBallence"
                android:layout_alignTop="@id/txtBallence"
                android:layout_alignRight="@id/txtBallence"
                android:layout_alignBottom="@id/txtBallence"
                android:scaleType="fitXY"
                android:src="@drawable/ic_dt_user_conis" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:layout_marginLeft="22dp"
                android:layout_marginRight="2dp"
                android:gravity="center_vertical"
                android:minWidth="150dp"
                android:paddingLeft="45dp"
                android:text="0"
                android:textColor="@color/gold_color"
                android:textSize="14dp"
                android:textStyle="bold" />
        </RelativeLayout>
    </RelativeLayout>
    <LinearLayout
        android:id="@+id/lnrtopviewcontainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="5dp"
        android:orientation="vertical"
        android:paddingLeft="@dimen/dimen_15dp"
        android:paddingTop="5dp"
        android:paddingRight="@dimen/dimen_15dp"
        android:layout_marginBottom="@dimen/dp10"
        >

        <ImageView
            android:id="@+id/ivDiceCircle"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="right"
            android:src="@drawable/ic_circle_dice"
            />


    </LinearLayout>
    <RelativeLayout
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp80"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp70"
        android:layout_weight="1">

        <ImageView
            android:layout_width="@dimen/dp80"
            android:layout_height="@dimen/dp80"
            android:layout_centerHorizontal="true"
            android:scaleType="centerCrop"
            android:src="@drawable/watch"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tvStartTimer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:shadowColor="#5d5534"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/payu_dimen_28dp"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:text="00"
            android:textColor="#5d5534"
            android:textSize="@dimen/dp20"
            android:visibility="visible" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/sticker_animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@drawable/winner_patti"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text="You Win"
                android:textColor="@color/white"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivWine"
                app:layout_constraintEnd_toEndOf="@+id/ivWine"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivWine" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivlose"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/ic_game_over"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvlose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/BrownColor"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivlose"
                app:layout_constraintEnd_toEndOf="@+id/ivlose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivlose" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltOngoinGame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:id="@+id/txtGameRunning"
            android:layout_width="@dimen/dp300"
            android:layout_height="@dimen/dp50"
            android:src="@drawable/waiting_for_next"
            android:visibility="visible" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp90"
        android:weightSum="4"
        android:visibility="invisible"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:id="@+id/left_user1"
            android:gravity="center"
            android:text="User 1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:id="@+id/left_user2"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 2" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="250dp"
            android:id="@+id/left_user3"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 3" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:id="@+id/left_user4"
            android:visibility="invisible"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 4" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp100"
        android:layout_alignParentEnd="true"
        android:weightSum="4"
        android:visibility="invisible"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:id="@+id/right_user1"
            android:layout_weight="1"
            android:layout_gravity="start|center"
            android:text="User 1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:id="@+id/right_user2"
            android:layout_weight="1"
            android:layout_gravity="start|center"
            android:text="User 2" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="250dp"
            android:id="@+id/right_user3"
            android:layout_weight="1"
            android:layout_gravity="start|center"
            android:text="User 3" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:id="@+id/right_user4"
            android:visibility="invisible"
            android:layout_weight="1"
            android:layout_gravity="start|center"
            android:text="User 4" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_bots"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="false"
        android:layout_centerInParent="true"
        tools:listitem="@layout/item_bots"
        tools:itemCount="3"
        android:layout_marginLeft="@dimen/dp60"
        android:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="visible"
        android:layout_alignParentEnd="true"
        android:layout_centerHorizontal="true"
        android:layout_marginRight="@dimen/dp70"
        tools:listitem="@layout/item_bots"
        tools:itemCount="3"
        android:layout_centerInParent="true"
        android:id="@+id/recycle_bots_right"/>


</RelativeLayout>