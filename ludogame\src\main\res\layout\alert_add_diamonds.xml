<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="20dp"
        android:paddingTop="40dp"
        android:paddingBottom="20dp"
        android:gravity="center"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_marginTop="20dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Buy Diamond"
                android:textColor="@color/white"
                android:fontFamily="@font/mama_bear"
                android:layout_marginBottom="20dp"
                android:layout_gravity="center"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:paddingLeft="10dp"
                android:background="@drawable/dice"
                android:backgroundTint="#44000000"
                android:paddingVertical="10dp"
                android:gravity="center_vertical"
                >
                <RelativeLayout
                    android:layout_width="150dp"
                    android:layout_height="80dp">
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/small_diamonds"
                        android:adjustViewBounds="true"
                        android:paddingVertical="20dp"
                        android:paddingRight="20dp"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:text="x 10"
                        android:textColor="@color/white"
                        android:fontFamily="@font/mama_bear"
                        android:textSize="20dp"
                        android:shadowColor="@android:color/black"
                        android:shadowRadius="20"
                        />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    >
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="10"
                            android:gravity="right"
                            android:textSize="20dp"
                            android:textColor="@color/white"
                            android:fontFamily="@font/mama_bear"
                            android:layout_marginRight="5dp"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Diamond"
                            android:textColor="@color/white"
                            />
                    </LinearLayout>


                    <Button
                        android:id="@+id/small_diamonds"
                        android:layout_width="100dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:background="@drawable/button_background_green"
                        android:textColor="@color/white"
                        android:text="Buy"
                        android:fontFamily="@font/mama_bear"
                        />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:paddingLeft="10dp"
                android:background="@drawable/dice"
                android:backgroundTint="#44000000"
                android:paddingVertical="10dp"
                >
                <RelativeLayout
                    android:layout_width="150dp"
                    android:layout_height="80dp">
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/medium_diamonds"
                        android:adjustViewBounds="true"
                        android:paddingVertical="10dp"
                        android:paddingRight="20dp"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:text="x 100"
                        android:textColor="@color/white"
                        android:fontFamily="@font/mama_bear"
                        android:textSize="20dp"
                        android:shadowColor="@android:color/black"
                        android:shadowRadius="20"
                        />
                </RelativeLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    >
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="100"
                            android:gravity="right"
                            android:textSize="20dp"
                            android:textColor="@color/white"
                            android:fontFamily="@font/mama_bear"
                            android:layout_marginRight="5dp"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Diamond"
                            android:textColor="@color/white"
                            />
                    </LinearLayout>


                    <Button
                        android:id="@+id/medium_diamonds"
                        android:layout_width="100dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:background="@drawable/button_background_green"
                        android:textColor="@color/white"
                        android:text="Buy"
                        android:fontFamily="@font/mama_bear"
                        />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/dice"
                android:backgroundTint="#44000000"
                android:paddingVertical="10dp"
                android:paddingLeft="10dp"
                >
                <RelativeLayout
                    android:layout_width="150dp"
                    android:layout_height="80dp">
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/large_diamonds"
                        android:adjustViewBounds="true"
                        android:paddingVertical="10dp"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:text="x 500"
                        android:textColor="@color/white"
                        android:fontFamily="@font/mama_bear"
                        android:textSize="20dp"
                        android:shadowColor="@android:color/black"
                        android:shadowRadius="20"
                        />
                </RelativeLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    >
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="500"
                            android:gravity="right"
                            android:textSize="20dp"
                            android:textColor="@color/white"
                            android:fontFamily="@font/mama_bear"
                            android:layout_marginRight="5dp"
                            />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Diamond"
                            android:textColor="@color/white"
                            />
                    </LinearLayout>


                    <Button
                        android:id="@+id/large_diamonds"
                        android:layout_width="100dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:background="@drawable/button_background_green"
                        android:textColor="@color/white"
                        android:text="Buy"
                        android:fontFamily="@font/mama_bear"
                        />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
    <TextView
        android:layout_width="190dp"
        android:layout_height="40dp"
        android:background="@drawable/play_local_written"
        android:layout_centerHorizontal="true"
        android:backgroundTint="@color/yellow3"
        android:text="Shop"
        android:gravity="center"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold"
        android:fontFamily="@font/oswald"
        android:elegantTextHeight="true"
        />
</RelativeLayout>