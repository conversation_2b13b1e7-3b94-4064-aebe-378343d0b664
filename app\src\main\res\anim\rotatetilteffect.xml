<?xml version="1.1" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Rotation animation for left to right movement -->
    <rotate
        android:duration="1000"
        android:fromDegrees="0"
        android:toDegrees="30"
    android:interpolator="@android:anim/linear_interpolator"
    android:pivotX="50%"
    android:pivotY="50%"
    android:repeatCount="infinite"
    android:fillAfter="true" />

    <!-- Tilt effect using scaling -->
    <scale
        android:duration="1000"
        android:fromXScale="1"
        android:toXScale="1.1"
    android:fromYScale="1"
    android:toYScale="0.9"
    android:pivotX="50%"
    android:pivotY="50%"
    android:repeatCount="infinite"
    android:repeatMode="reverse"/>

</set>
