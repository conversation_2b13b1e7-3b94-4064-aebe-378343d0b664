<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp7"
        android:background="@drawable/d_jackpot_rulebg"
        android:padding="@dimen/dp7"
        android:weightSum="2">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tvRules"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.5"
                android:text="Result"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tvRulespoint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Payoff"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>