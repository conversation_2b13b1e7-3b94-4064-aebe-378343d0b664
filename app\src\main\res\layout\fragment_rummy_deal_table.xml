<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/dialogParentStyle"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="25dp"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        style="@style/popUpBoxbg"
        android:padding="20dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp30"
            android:gravity="center"
            >

            <TextView
                android:id="@+id/tv_2player"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="cursive"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:text="Select Deal"
                android:layout_marginBottom="@dimen/dp20"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/lnrSelectTable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="@dimen/dp20"
                >

                <TextView
                    android:id="@+id/tvDeal2"
                    android:layout_width="55dp"
                    android:layout_height="55dp"
                    android:text="2"
                    android:background="@drawable/ic_circle_red_actvite"
                    android:gravity="center"
                    android:textSize="22sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:layout_marginRight="@dimen/dp20"
                    />

                <TextView
                    android:id="@+id/tvDeal3"
                    android:layout_width="55dp"
                    android:layout_height="55dp"
                    android:text="3"
                    android:background="@drawable/ic_circle_red_unactvite"
                    android:gravity="center"
                    android:textSize="22sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    />

            </LinearLayout>

            <TextView
                android:id="@+id/tvSelectBoot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="cursive"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:text=""
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_play"
                android:layout_width="150dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/ic_btn_play"
                android:gravity="center"
                android:textAllCaps="true"
                android:textSize="22sp"
                android:textStyle="bold" />

        </LinearLayout>

    </RelativeLayout>

    <include
        layout="@layout/dialog_toolbar"/>

</RelativeLayout>