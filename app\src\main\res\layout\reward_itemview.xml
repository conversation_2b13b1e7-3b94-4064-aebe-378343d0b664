<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/parentlayout"
    android:layout_width="180dp"
    android:layout_height="220dp"
    android:layout_margin="5dp"
    app:cardUseCompatPadding="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/frame"
        android:gravity="center"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            >

            <ImageView
                android:id="@+id/imgreward"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/bulkchipsgreen" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/imgreward"
                android:layout_marginStart="@dimen/dp10"
                android:gravity="center_horizontal">

                <TextView
                    android:id="@+id/txtcoins"
                    android:layout_width="@dimen/payu_dimen_100dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ic_button"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="1,900"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtdays"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center_vertical"
                    android:padding="5dp"
                    android:text="Day1"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rlt_collected"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/dp40"
                android:src="@drawable/correct" />
        </RelativeLayout>
    </RelativeLayout>


</RelativeLayout>