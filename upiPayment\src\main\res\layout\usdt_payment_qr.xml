<?xml version="1.1" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="USDT Payment"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:id="@+id/txt_payment_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Amount: 0.00 USDT"
        android:textSize="18sp"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Scan QR code to send payment"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:textColor="@android:color/black"/>

    <ImageView
        android:id="@+id/img_qr_code"
        android:layout_width="250dp"
        android:layout_height="250dp"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:background="@android:color/white"
        android:contentDescription="QR Code for wallet address" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Wallet Address"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:textColor="@android:color/black"/>

    <TextView
        android:id="@+id/txt_wallet_address"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="******************************************"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:textColor="@android:color/black"
        android:padding="8dp"
        android:background="#f0f0f0"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_copy_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Copy Address"
            android:layout_marginEnd="8dp"/>

        <Button
            android:id="@+id/btn_check_payment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Check Payment"/>

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Important: Send only USDT on the BSC network to this address. Other tokens or networks may result in permanent loss of funds."
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="#FF0000"/>

    <TextView
        android:id="@+id/txt_payment_timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Time remaining: 05:00"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="@android:color/black"/>

</LinearLayout>
