<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >


    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@+id/lnrParentContainer"
        android:layout_alignTop="@+id/lnrParentContainer"
        android:layout_alignRight="@+id/lnrParentContainer"
        android:layout_alignBottom="@+id/lnrParentContainer"
        android:background="@drawable/d_jackpot_rulebg" />

    <LinearLayout
        android:id="@+id/lnrParentContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp10"
        android:paddingHorizontal="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp10"
        >

        <TextView
            android:id="@+id/tvSerial"
            style="?textAppearanceBodyMedium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginRight="@dimen/dp15"
            android:textSize="17sp"
            android:textColor="@color/white"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="1"
            >

            <TextView
                android:id="@+id/tvRanking"
                style="?textAppearanceBodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Ranking"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvSerial"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                />

            <TextView
                android:id="@+id/tvPrice"
                style="?textAppearanceBodyMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Price"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvSerial"
                android:textSize="17sp"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                />

    </LinearLayout>

    </LinearLayout>
</RelativeLayout>