<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp40"
    android:layout_marginBottom="10dp"
    app:cardUseCompatPadding="true"
    app:cardElevation="1dp"
    app:cardCornerRadius="10dp"
    android:background="@drawable/transpernt_purple"
    android:clickable="true"
    android:foreground="?selectableItemBackground"
    android:focusable="true"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_centerVertical="true"
        android:layout_height="wrap_content">

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/lnr_parent"
            android:padding="10dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp"
                >

                <LinearLayout
                    android:id="@+id/lnrSerial"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.4"
                    >

                    <TextView
                        android:id="@+id/tvSerial"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="1"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    >

                    <TextView
                        android:id="@+id/tvNanme"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Name"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lnrEmail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:visibility="gone"
                    >

                    <TextView
                        android:id="@+id/tvEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Email"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lnrReferalid"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:visibility="gone"
                    >

                    <TextView
                        android:id="@+id/tvReferalid"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Referral"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>



                <LinearLayout
                    android:id="@+id/lnrCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1"
                    >

                    <TextView
                        android:id="@+id/txtammount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Coin"
                        android:textColor="@color/green"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrCount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1"
                    >

                    <TextView
                        android:id="@+id/txtcount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Count"
                        android:textColor="@color/green"
                        android:gravity="center"
                        android:textStyle="bold"
                        />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lnrGame"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    >

                    <TextView
                        android:id="@+id/tvAddedDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:text="Added Date"
                        android:textColor="@color/white"
                        android:gravity="center"
                        android:textStyle="bold"

                        />

                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginLeft="16dp"
                android:layout_gravity="center"
                android:visibility="gone"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:text="Table Id : "
                    android:id="@+id/txtid"
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="18sp"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:text="User Name"
                    android:id="@+id/txtusername"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:fontFamily="casual"
                    />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal"
                    >

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/bulkchipsred"
                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:text="Ammout : "
                        android:layout_marginLeft="6dp"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:fontFamily="monospace"
                        />

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/imgreward"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/reward"
            android:tint="@color/rewardGold"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"

            />
    </RelativeLayout>

</RelativeLayout>