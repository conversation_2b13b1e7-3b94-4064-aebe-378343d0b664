<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    style="@style/dialogParentStyle">


<!--    <ImageView-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        style="@style/popUpBoxbg"-->
<!--        android:layout_alignLeft="@id/lnr_box"-->
<!--        android:layout_alignRight="@id/lnr_box"-->
<!--        android:layout_alignTop="@id/lnr_box"-->
<!--        android:layout_alignBottom="@id/lnr_box"-->
<!--        />-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:id="@+id/lnr_box"
        android:background="@drawable/d_pop_setting"
        android:layout_below="@+id/rtl_toolbar"
        android:layout_marginTop="@dimen/pop_up_top_margin"
        android:layout_marginHorizontal="25dp"
        android:padding="20dp"
        >


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginBottom="@dimen/dp20"
            >

            <LinearLayout
                android:id="@+id/lnrRuleslist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp20"
                android:paddingBottom="@dimen/dp20"
                >

                <TextView
                    android:id="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="@string/how_to_play"
                    android:textAlignment="center"
                    android:textSize="26sp"
                    android:visibility="gone"
                    android:background="@color/cb_dark_blue_button"
                    android:layout_width="match_parent"
                    android:paddingTop="@dimen/dp5"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_below="@+id/how_to"
                    android:textColor="@color/white"
                    android:text="
** Games Rules **\n\n
  - The dealer will deal with one Rs. Indian coin on each game\n\n

 - one for the HEAD of the coin and one for the TAIL of the coin.\n\n

 - Simply makes a bet on HEAD or TAIL, which side of the coin \n      will comes on tossed, that bet will be a win.\n\n

   Paid for HEAD - 1:1\n
    Paid for TAIL - 1:1
"
                    android:textAlignment="textStart"
                    android:layout_marginLeft="@dimen/dp50"
                    android:layout_marginRight="@dimen/dp50"
                    android:textSize="@dimen/sp16"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp20"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="300dp"
                    android:visibility="invisible"
                    android:background="@drawable/ic_dt_rule1"
                    />

            </LinearLayout>


        </ScrollView>

    </LinearLayout>


    <include
        layout="@layout/dialog_toolbar"/>



</RelativeLayout>