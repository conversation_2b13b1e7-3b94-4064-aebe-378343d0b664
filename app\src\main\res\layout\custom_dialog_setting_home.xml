<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:gravity="right">


        <ImageView
            style="@style/popUpBoxbg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@id/detailsContainer"
            android:layout_alignTop="@id/detailsContainer"
            android:layout_alignRight="@id/detailsContainer"
            android:layout_alignBottom="@id/detailsContainer" />

        <HorizontalScrollView
            android:layout_below="@+id/rtl_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-40dp"
            android:scrollbars="horizontal">

            <RelativeLayout
                android:id="@+id/detailsContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dp40"
                android:paddingRight="@dimen/dp40"
                android:paddingLeft="@dimen/dp40"
                android:minWidth="600dp"
                >

            <LinearLayout
                android:id="@+id/lnrdetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp15"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/rltimageptofile"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal">

                            <de.hdodenhof.circleimageview.CircleImageView
                                android:id="@+id/imaprofile"
                                android:layout_width="70dp"
                                android:layout_height="70dp"
                                android:layout_centerVertical="true"
                                android:layout_gravity="center_horizontal"
                                android:background="@drawable/gold_round"
                                android:padding="@dimen/dp10"
                                android:src="@drawable/user_photo" />

                            <ImageView
                                android:id="@+id/imgCamera"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_alignEnd="@id/imaprofile"
                                android:layout_alignBottom="@id/imaprofile"
                                android:layout_marginEnd="5dp"
                                android:layout_marginBottom="5dp"
                                android:background="@drawable/gold_round"
                                android:padding="5dp"
                                android:src="@drawable/ic_baseline_camera"
                                android:scaleType="centerInside" />

                        </RelativeLayout>

                        <TextView
                            android:id="@+id/tv_playername"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/dp5"
                            android:gravity="center"
                            android:text="John Smith"
                            android:textColor="@color/Golder_yellow"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/dp5"
                            android:gravity="center"
                            android:text="Player ID:"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_playerid"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/ic_gredient_yellow"
                            android:gravity="center"
                            android:padding="@dimen/dp10"
                            android:text="#000"
                            android:textColor="@color/new_yellow"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="150dp"
                        android:layout_height="220dp"
                        android:layout_gravity="center"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/frame"
                        android:gravity="center">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/btm"
                                android:layout_width="wrap_content"
                                android:layout_height="40dp"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="10dp"
                                android:gravity="center_horizontal">

                                <TextView
                                    android:id="@+id/txt_sound"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentLeft="true"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="10dp"
                                    android:text="Off Sound"
                                    android:textColor="@color/colordullwhite"
                                    android:textSize="15dp" />

                                <Switch
                                    android:id="@+id/switch1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="15dp"
                                    android:layout_marginRight="15dp"
                                    android:buttonTint="@color/new_yellow"
                                    android:text=""
                                    tools:ignore="UseSwitchCompatOrMaterialXml" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/vibrate"
                                android:layout_width="wrap_content"
                                android:layout_height="40dp"
                                android:layout_below="@+id/btm"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="10dp"
                                android:gravity="center_horizontal">

                                <TextView
                                    android:id="@+id/txt_vibrate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentLeft="true"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="10dp"
                                    android:text="Off Vibrate"
                                    android:textColor="@color/colordullwhite"
                                    android:textSize="15dp" />

                                <Switch
                                    android:id="@+id/switch2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginLeft="15dp"
                                    android:layout_marginRight="15dp"
                                    android:buttonTint="@color/new_yellow"
                                    android:text="" />
                            </LinearLayout>

                        </RelativeLayout>

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="140dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/frame"
                        android:gravity="center">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/btnPrivacy"
                                android:layout_width="120dp"
                                android:layout_height="@dimen/dp40"
                                android:background="@drawable/round_bor_colored"
                                android:padding="@dimen/dp5">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:text="Privacy Policy "
                                    android:textColor="@color/white"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/btnTermscond"
                                android:layout_width="120dp"
                                android:layout_height="@dimen/dp40"
                                android:layout_below="@id/btnPrivacy"
                                android:layout_marginTop="@dimen/dp10"
                                android:background="@drawable/round_bor_colored"
                                android:padding="@dimen/dp5">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:text="T &amp; C"
                                    android:textColor="@color/white"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </RelativeLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="140dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/frame"
                        android:gravity="center">

                        <RelativeLayout
                            android:id="@+id/rel_layout"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/btnrefund"
                                android:layout_width="120dp"
                                android:layout_height="@dimen/dp40"
                                android:layout_marginTop="@dimen/dp10"
                                android:background="@drawable/round_bor_colored"
                                android:gravity="center_horizontal"
                                android:padding="@dimen/dp5">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:text="Refund Policy"
                                    android:textColor="@color/white"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/btnHelpandsupport"
                                android:layout_width="120dp"
                                android:layout_height="@dimen/dp40"
                                android:layout_below="@id/btnrefund"
                                android:layout_marginTop="@dimen/dp10"
                                android:background="@drawable/round_bor_colored"
                                android:gravity="center_horizontal"
                                android:padding="@dimen/dp5">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:text="Support"
                                    android:textColor="@color/white"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </RelativeLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="140dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:background="@drawable/frame"
                        android:gravity="center">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <LinearLayout
                                android:id="@+id/btnUpdateProfile"
                                android:layout_width="120dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp10"
                                android:background="@drawable/round_bor_colored"
                                android:gravity="center_horizontal"
                                android:padding="@dimen/dp5">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:text="Update Profile"
                                    android:textColor="@color/white"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </RelativeLayout>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_below="@+id/lnrdetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:gravity="center"
                >

                <LinearLayout
                    android:id="@+id/lnr_language"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="1"
                    >

                    <include layout="@layout/item_language" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lnrlogoutdia"
                    android:layout_width="@dimen/payu_dimen_100dp"
                    android:layout_height="@dimen/dp40"
                    android:background="@drawable/btn_logout"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp5"
                    android:layout_alignParentRight="true"
                    >
                </LinearLayout>
            </LinearLayout>


        </RelativeLayout>

        </HorizontalScrollView>

        <include layout="@layout/dialog_toolbar" />

    </RelativeLayout>

</RelativeLayout>