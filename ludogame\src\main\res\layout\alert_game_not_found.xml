<?xml version="1.1" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/alert_dialogue_background_3"
    android:padding="20dp"
    android:layout_marginHorizontal="40dp"
    >
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="20dp"
        android:fontFamily="@font/oswald"
        android:text="Game Not Found\nPlease try again after sometime."
        android:gravity="center"
        android:layout_gravity="center"
        android:textColor="@color/white"
        android:textStyle="bold"

        />
    <Button
        android:id="@+id/room_not_found_button"
        android:layout_width="150dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:text="okay"
        android:background="@drawable/button_background_red"
        android:fontFamily="@font/mama_bear"
        android:layout_marginTop="30dp"
        android:textColor="@color/white"
        />

</LinearLayout>