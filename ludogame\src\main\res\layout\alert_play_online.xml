<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="40dp"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="40dp"
        android:paddingTop="40dp"
        android:paddingBottom="20dp"
        android:gravity="center"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_marginTop="20dp"
        >

        <Button
            android:id="@+id/home_create_room_button"
            android:layout_width="170dp"
            android:layout_height="70dp"
            android:layout_marginTop="20dp"
            android:src="@drawable/start_button"
            android:adjustViewBounds="true"
            android:scaleType="centerInside"
            android:background="@drawable/button_background_green"
            android:text="Create\nRoom"
            android:fontFamily="@font/mama_bear"
            android:textSize="22dp"
            android:textColor="@color/white"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:text="or"
            android:fontFamily="@font/mama_bear"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            />
        <Button
            android:id="@+id/home_join_room_button"
            android:layout_width="170dp"
            android:layout_height="70dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/start_button"
            android:adjustViewBounds="true"
            android:scaleType="centerInside"
            android:background="@drawable/button_backgrond"
            android:text="Join\nRoom"
            android:fontFamily="@font/mama_bear"
            android:textSize="22dp"
            android:textColor="@color/white"
            android:layout_marginBottom="10dp"
            />
    </LinearLayout>
    <TextView
        android:layout_width="190dp"
        android:layout_height="40dp"
        android:background="@drawable/play_local_written"
        android:layout_centerHorizontal="true"
        android:backgroundTint="@color/yellow3"
        android:text="PLay Online"
        android:gravity="center"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold"
        android:fontFamily="@font/oswald"
        />
</RelativeLayout>